2025-07-25 18:25:41.257 [info] (Client) Pylance client (2025.7.1) started with python extension (2025.10.1)
2025-07-25 18:25:45.818 [info] (11916) Server root directory: file:///c%3A/Users/<USER>/.vscode/extensions/ms-python.vscode-pylance-2025.7.1/dist
2025-07-25 18:25:45.821 [info] (11916) Pylance language server 2025.7.1 (pyright version 1.1.403, commit 8b02aa84) starting
2025-07-25 18:25:45.891 [info] (11916) Starting service instance "augment_code-vip" for workspace "c:\Users\<USER>\Desktop\augment_code-vip"
2025-07-25 18:25:46.106 [info] (11916) Starting Mcp server id:4588 ...
2025-07-25 18:25:46.352 [info] (11916) Setting environmentName for service "augment_code-vip": "3.13.5 (global)"
2025-07-25 18:25:46.362 [info] (11916) Setting pythonPath for service "augment_code-vip": "C:\Python313\python.exe"
2025-07-25 18:25:46.366 [info] (11916) No include entries specified; assuming c:\Users\<USER>\Desktop\augment_code-vip
2025-07-25 18:25:46.367 [info] (11916) Auto-excluding **/node_modules
2025-07-25 18:25:46.377 [info] (11916) Auto-excluding **/__pycache__
2025-07-25 18:25:46.378 [info] (11916) Auto-excluding **/.*
2025-07-25 18:25:47.029 [info] (11916) Assuming Python version 3.13.5.final.0
2025-07-25 18:25:47.895 [info] (11916) Found 7 source files
2025-07-25 18:25:47.934 [info] (11916) Settings have not changed. Skipping update.
2025-07-25 18:25:47.936 [info] (11916) virtual workspace: vscode-chat-code-block://be677647-dc45-407e-acb3-19263217a450/
2025-07-25 18:25:47.939 [info] (11916) Starting service instance "<default>" for workspace "vscode-chat-code-block://be677647-dc45-407e-acb3-19263217a450/"
2025-07-25 18:25:49.315 [info] (11916) Setting environmentName for service "<default>": "3.13.5 (global)"
2025-07-25 18:25:49.316 [info] (11916) No include entries specified; assuming vscode-chat-code-block://be677647-dc45-407e-acb3-19263217a450/
2025-07-25 18:25:49.316 [info] (11916) Auto-excluding **/node_modules
2025-07-25 18:25:49.317 [info] (11916) Auto-excluding **/__pycache__
2025-07-25 18:25:49.317 [info] (11916) Auto-excluding **/.*
2025-07-25 18:25:49.937 [info] (11916) Assuming Python version 3.13.5.final.0
2025-07-25 18:25:50.612 [error] (11916) File or directory "vscode-chat-code-block://be677647-dc45-407e-acb3-19263217a450/" does not exist.
2025-07-25 18:25:50.612 [info] (11916) No source files found.
2025-07-25 18:25:52.053 [info] (11916) BG: Priority queue background worker(2) root directory: file:///c%3A/Users/<USER>/.vscode/extensions/ms-python.vscode-pylance-2025.7.1/dist
2025-07-25 18:25:52.054 [info] (11916) BG: Priority queue background worker(2) started
2025-07-25 18:25:52.137 [info] (11916) BG: Indexer background runner(3) root directory: file:///c%3A/Users/<USER>/.vscode/extensions/ms-python.vscode-pylance-2025.7.1/dist (index)
2025-07-25 18:25:52.138 [info] (11916) BG: Indexing(3) started
2025-07-25 18:26:03.892 [info] (11916) BG: [IDX(3)] Long operation: scan packages file:///c%3A/Users/<USER>/Desktop/augment_code-vip (11529ms)
2025-07-25 18:26:03.893 [info] (11916) BG: scanned(3) 19 files over 1 exec env
2025-07-25 18:26:07.413 [info] (11916) BG: [IDX(3)] Long operation: index execution environment file:///c%3A/Users/<USER>/Desktop/augment_code-vip (2891ms)
2025-07-25 18:26:07.470 [info] (11916) BG: [IDX(3)] Long operation: index packages file:///c%3A/Users/<USER>/Desktop/augment_code-vip (2976ms)
2025-07-25 18:26:07.471 [info] (11916) BG: indexed(3) 19 files over 1 exec env
2025-07-25 18:26:07.497 [info] (11916) BG: Indexing finished(3).
2025-07-25 18:34:46.145 [info] (11916) BG: [BG(1)] Long operation: SemanticTokens full at file:///c%3A/Users/<USER>/Desktop/augment_code-vip/augment_cleaner_v2.py (2370ms)
2025-07-25 18:39:11.767 [info] (11916) BG: [BG(1)] Long operation: SemanticTokens delta previousResultId:1753464883761 at file:///c%3A/Users/<USER>/Desktop/augment_code-vip/augment_cleaner_v2.py (2082ms)
