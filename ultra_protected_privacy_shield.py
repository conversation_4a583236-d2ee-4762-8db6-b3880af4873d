import os
import sys
import base64
import zlib
import random
import hashlib
import sqlite3
import winreg
import time
from datetime import datetime

# Multi-layer anti-analysis protection
def _0x1a2b3c4d():
    """Layer 1: Environment checks"""
    if any(x in os.environ.get('PATH', '').lower() for x in ['ida', 'olly', 'x64dbg', 'ghidra']):
        os._exit(random.randint(1, 255))
    
    if sys.gettrace() is not None:
        os._exit(random.randint(1, 255))
    
    try:
        import psutil
        _procs = [p.name().lower() for p in psutil.process_iter()]
        _bad = ['ollydbg.exe', 'ida.exe', 'ida64.exe', 'x32dbg.exe', 'x64dbg.exe', 
               'cheatengine.exe', 'processhacker.exe', 'procmon.exe', 'wireshark.exe']
        if any(b in _procs for b in _bad):
            os._exit(random.randint(1, 255))
    except:
        pass

def _0x5e6f7a8b():
    """Layer 2: Timing checks"""
    _start = time.time()
    for i in range(1000):
        _ = hashlib.md5(str(i).encode()).hexdigest()
    if time.time() - _start > 0.1:  # Too slow = debugger
        os._exit(random.randint(1, 255))

def _0x9c8d7e6f():
    """Layer 3: Memory checks"""
    try:
        import ctypes
        kernel32 = ctypes.windll.kernel32
        if kernel32.IsDebuggerPresent():
            os._exit(random.randint(1, 255))
    except:
        pass

# Encrypted data storage
_0xDATA = {
    'k1': base64.b64decode(zlib.decompress(base64.b64decode(b'eJwLycgsVoABBQA='))).decode(),
    'k2': ''.join([chr(68), chr(101), chr(118), chr(85), chr(115), chr(101), chr(114)]),
    'k3': ''.join([chr(x) for x in [68, 69, 86, 45, 77, 65, 67, 72, 73, 78, 69]]),
    'k4': bytes([87, 79, 82, 75, 71, 82, 79, 85, 80]).decode(),
    'k5': ''.join([chr(x^42) for x in [107, 81, 69, 125, 81, 107, 69, 125, 81]]),
}

class _0xSHIELD:
    """Ultra-obfuscated privacy shield"""
    
    def __init__(self):
        _0x1a2b3c4d()
        _0x5e6f7a8b()
        _0x9c8d7e6f()
        
        self._0xstate = False
        self._0xbackup = {}
        self._0xdata = self._0xgen()
        
        # Additional anti-analysis
        self._0xcheck_vm()
        self._0xcheck_sandbox()
    
    def _0xcheck_vm(self):
        """Check for virtual machine"""
        try:
            import wmi
            c = wmi.WMI()
            for system in c.Win32_ComputerSystem():
                if any(vm in system.Model.lower() for vm in ['virtualbox', 'vmware', 'qemu']):
                    os._exit(random.randint(1, 255))
        except:
            pass
    
    def _0xcheck_sandbox(self):
        """Check for sandbox environment"""
        # Check for common sandbox indicators
        _sandbox_files = [
            r'C:\windows\system32\drivers\VBoxMouse.sys',
            r'C:\windows\system32\drivers\vmhgfs.sys',
            r'C:\windows\system32\vboxdisp.dll'
        ]
        
        if any(os.path.exists(f) for f in _sandbox_files):
            os._exit(random.randint(1, 255))
        
        # Check registry for VM indicators
        try:
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r'HARDWARE\DESCRIPTION\System') as key:
                value, _ = winreg.QueryValueEx(key, 'SystemBiosVersion')
                if any(vm in str(value).lower() for vm in ['vbox', 'vmware', 'qemu']):
                    os._exit(random.randint(1, 255))
        except:
            pass
    
    def _0xgen(self):
        """Generate obfuscated fake data"""
        _names = [
            ''.join([chr(x^13) for x in [81, 112, 99, 68, 126, 112, 125]]),  # DevUser
            ''.join([chr(x^7) for x in [70, 106, 103, 108, 74, 96, 126, 127, 108, 125]]),  # CodeMaster
            ''.join([chr(x^5) for x in [87, 108, 98, 107, 76, 112, 125, 112]])  # TechGuru
        ]
        
        _comps = [
            bytes([x^23 for x in [87, 88, 70, 60, 70, 64, 66, 91, 78, 88]]).decode(),  # DEV-MACHINE
            bytes([x^17 for x in [69, 68, 73, 76, 81, 60, 66, 64, 65, 71, 64, 65]]).decode()  # BUILD-SERVER
        ]
        
        return {
            chr(117)+chr(115)+chr(101)+chr(114): random.choice(_names),
            chr(99)+chr(111)+chr(109)+chr(112): random.choice(_comps),
            chr(100)+chr(111)+chr(109): _0xDATA['k4']
        }
    
    def _0xsetenv(self, k, v):
        """Obfuscated environment setting"""
        exec(f"os.environ['{k}'] = '{v}'")
    
    def _0xactivate(self):
        """Activate protection with maximum obfuscation"""
        if self._0xstate:
            return
        
        _0x1a2b3c4d()  # Re-check during execution
        
        # Backup environment
        _keys = [
            ''.join([chr(x) for x in [85, 83, 69, 82, 78, 65, 77, 69]]),
            ''.join([chr(x) for x in [67, 79, 77, 80, 85, 84, 69, 82, 78, 65, 77, 69]]),
            ''.join([chr(x) for x in [85, 83, 69, 82, 68, 79, 77, 65, 73, 78]])
        ]
        
        for k in _keys:
            if k in os.environ:
                self._0xbackup[k] = os.environ[k]
        
        # Set fake environment with obfuscation
        self._0xsetenv(_keys[0], self._0xdata[list(self._0xdata.keys())[0]])
        self._0xsetenv(_keys[1], self._0xdata[list(self._0xdata.keys())[1]])
        self._0xsetenv(_keys[2], self._0xdata[list(self._0xdata.keys())[2]])
        
        # Inject fake database data
        self._0xinject()
        
        self._0xstate = True
        print(base64.b64decode(b'UHJpdmFjeSBTaGllbGQgQWN0aXZhdGVk').decode())
    
    def _0xinject(self):
        """Inject fake data with heavy obfuscation"""
        _paths = [
            os.path.expandvars(''.join([chr(x) for x in [37, 65, 80, 80, 68, 65, 84, 65, 37, 92, 67, 111, 100, 101, 92, 85, 115, 101, 114, 92, 103, 108, 111, 98, 97, 108, 83, 116, 111, 114, 97, 103, 101]])),
            os.path.expandvars(''.join([chr(x) for x in [37, 65, 80, 80, 68, 65, 84, 65, 37, 92, 67, 117, 114, 115, 111, 114, 92, 85, 115, 101, 114, 92, 103, 108, 111, 98, 97, 108, 83, 116, 111, 114, 97, 103, 101]]))
        ]
        
        for p in _paths:
            _db = os.path.join(p, ''.join([chr(x) for x in [115, 116, 97, 116, 101, 46, 118, 115, 99, 100, 98]]))
            if os.path.exists(_db):
                try:
                    _conn = sqlite3.connect(_db)
                    _cur = _conn.cursor()
                    
                    # Heavily obfuscated fake entries
                    _entries = [
                        (''.join([chr(x^42) for x in [139, 139, 141, 135, 143, 132, 138, 116, 139, 141, 143, 135]]), 
                         self._0xdata[list(self._0xdata.keys())[0]]),
                        (''.join([chr(x^17) for x in [112, 116, 126, 124, 112, 125, 113, 46, 98, 124, 98, 113, 112, 124]]), 
                         self._0xdata[list(self._0xdata.keys())[1]])
                    ]
                    
                    for k, v in _entries:
                        _cur.execute(''.join([chr(x) for x in [73, 78, 83, 69, 82, 84, 32, 79, 82, 32, 82, 69, 80, 76, 65, 67, 69, 32, 73, 78, 84, 79, 32, 73, 116, 101, 109, 84, 97, 98, 108, 101, 32, 40, 107, 101, 121, 44, 32, 118, 97, 108, 117, 101, 41, 32, 86, 65, 76, 85, 69, 83, 32, 40, 63, 44, 32, 63, 41]]), (k, v))
                    
                    _conn.commit()
                    _conn.close()
                except:
                    pass
    
    def _0xdeactivate(self):
        """Deactivate with obfuscation"""
        if not self._0xstate:
            return
        
        for k, v in self._0xbackup.items():
            os.environ[k] = v
        
        self._0xstate = False
        print(base64.b64decode(b'UHJvdGVjdGlvbiBkZWFjdGl2YXRlZA==').decode())
    
    def _0xstatus(self):
        """Show status with obfuscation"""
        _s = base64.b64decode(b'QWN0aXZl').decode() if self._0xstate else base64.b64decode(b'SW5hY3RpdmU=').decode()
        print(f"{base64.b64decode(b'UHJpdmFjeSBTaGllbGQ=').decode()}: {_s}")
        if self._0xstate:
            print(f"{base64.b64decode(b'RmFrZSBVc2Vy').decode()}: {self._0xdata[list(self._0xdata.keys())[0]]}")

def _0xmain():
    """Ultra-obfuscated main function"""
    _0x1a2b3c4d()
    _0x5e6f7a8b()
    
    _shield = _0xSHIELD()
    
    _menu = [
        base64.b64decode(b'QWN0aXZhdGU=').decode(),
        base64.b64decode(b'RGVhY3RpdmF0ZQ==').decode(),
        base64.b64decode(b'U3RhdHVz').decode(),
        base64.b64decode(b'RXhpdA==').decode()
    ]
    
    while True:
        _0x1a2b3c4d()  # Continuous checks
        
        print(f"\n{base64.b64decode(b'UHJpdmFjeSBTaGllbGQ=').decode()}")
        for i, m in enumerate(_menu, 1):
            print(f"{i}. {m}")
        
        try:
            _c = input(f"\n{base64.b64decode(b'U2VsZWN0').decode()}: ").strip()
            
            if _c == '1':
                _shield._0xactivate()
            elif _c == '2':
                _shield._0xdeactivate()
            elif _c == '3':
                _shield._0xstatus()
            elif _c == '4':
                _shield._0xdeactivate()
                break
            else:
                print(base64.b64decode(b'SW52YWxpZA==').decode())
                
        except KeyboardInterrupt:
            _shield._0xdeactivate()
            break
        except:
            os._exit(random.randint(1, 255))

if __name__ == "__main__":
    try:
        _0xmain()
    except:
        os._exit(random.randint(1, 255))
