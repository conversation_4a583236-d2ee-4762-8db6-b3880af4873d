# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['augment_privacy_shield.py'],
    pathex=[],
    binaries=[],
    datas=[('shield_logs', 'shield_logs')],
    hiddenimports=['psutil', 'watchdog', 'watchdog.observers', 'watchdog.events', 'wmi', 'win32api', 'win32con', 'win32security', 'win32file'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='AugmentPrivacyShield',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
