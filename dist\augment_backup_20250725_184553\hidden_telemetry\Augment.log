2025-07-25 18:25:14.523 [info] 'AugmentConfigListener' settings parsed successfully
2025-07-25 18:25:14.523 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"disableFocusOnAugmentPanel":false,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-07-25 18:25:14.571 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","enableNewThreadsList":false,"enableUntruncatedContentStorage":false,"maxLinesTerminalProcessOutput":0,"vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","vscodeMinVersion":"1.96.0","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeSupportToolUseStartMinVersion":"","enableAgentAutoMode":false,"enableAgentSwarmMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5,"historySummaryMinVersion":"","historySummaryParams":"","enableCommitIndexing":false,"maxCommitsToIndex":0,"enableExchangeStorage":false,"conversationHistorySizeThresholdBytes":268435456,"enableToolUseStateStorage":false,"retryChatStreamTimeouts":false,"remoteAgentCurrentWorkspace":false,"enableMemoryRetrieval":false,"enableAgentTabs":false,"enableSwarmMode":false,"enableGroupedTools":false,"remoteAgentsResumeHintAvailableTtlDays":0,"enableParallelTools":false,"enableAgentGitTracker":false}
2025-07-25 18:25:14.571 [info] 'SidecarAnalytics' Segment analytics initialized for vscode
2025-07-25 18:25:14.571 [info] 'AugmentConfigListener' settings parsed successfully
2025-07-25 18:25:14.571 [info] 'AugmentConfigListener' settings parsed successfully
2025-07-25 18:25:14.571 [info] 'AugmentConfigListener' settings parsed successfully
2025-07-25 18:25:14.572 [info] 'AugmentConfigListener' settings parsed successfully
2025-07-25 18:25:14.572 [info] 'AugmentConfigListener' settings parsed successfully
2025-07-25 18:25:14.572 [info] 'AugmentExtension' Retrieving model config
2025-07-25 18:25:14.572 [info] 'AugmentConfigListener' settings parsed successfully
2025-07-25 18:25:14.572 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 8788 msec late.
2025-07-25 18:25:14.795 [info] 'AugmentExtension' Retrieved model config
2025-07-25 18:25:14.795 [info] 'AugmentExtension' Returning model config
2025-07-25 18:25:14.961 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - enableNewThreadsList: false to true
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 49512
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - vscodeDirectApplyMinVersion: "" to "0.499.0"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - enableRules: false to true
  - memoriesTextEditorEnabled: false to true
  - vscodeTaskListMinVersion: "" to "0.482.0"
  - vscodeSupportToolUseStartMinVersion: "" to "0.485.0"
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
  - historySummaryParams: "" to "{\"buffer_time_before_cache_expiration_ms\": 30000, \"cache_ttl_ms\": 300000, \"history_tail_size_chars_to_exclude\": 80000, \"prompt\": \"Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.\\nThis summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing with the conversation and supporting any continuing tasks.\\n\\nYour summary should be structured as follows:\\nContext: The context to continue the conversation with. If applicable based on the current task, this should include:\\n1. Previous Conversation: High level details about what was discussed throughout the entire conversation with the user. This should be written to allow someone to be able to follow the general overarching conversation flow.\\n2. Current Work: Describe in detail what was being worked on prior to this request to summarize the conversation. Pay special attention to the more recent messages in the conversation.\\n3. Key Technical Concepts: List all important technical concepts, technologies, coding conventions, and frameworks discussed, which might be relevant for continuing with this work.\\n4. Relevant Files and Code: If applicable, enumerate specific files and code sections examined, modified, or created for the task continuation. Pay special attention to the most recent messages and changes.\\n5. Problem Solving: Document problems solved thus far and any ongoing troubleshooting efforts.\\n6. Pending Tasks and Next Steps: Outline all pending tasks that you have explicitly been asked to work on, as well as list the next steps you will take for all outstanding work, if applicable. Include code snippets where they add clarity. For any next steps, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no information loss in context between tasks.\\n\\nExample summary structure:\\n1. Previous Conversation:\\n[Detailed description]\\n2. Current Work:\\n[Detailed description]\\n3. Key Technical Concepts:\\n- [Concept 1]\\n- [Concept 2]\\n- [...]\\n4. Relevant Files and Code:\\n- [File Name 1]\\n    - [Summary of why this file is important]\\n    - [Summary of the changes made to this file, if any]\\n    - [Important Code Snippet]\\n- [File Name 2]\\n    - [Important Code Snippet]\\n- [...]\\n5. Problem Solving:\\n[Detailed description]\\n6. Pending Tasks and Next Steps:\\n- [Task 1 details & next steps]\\n- [Task 2 details & next steps]\\n- [...]\\n\\nOutput only the summary of the conversation so far, without any additional commentary or explanation.\\n\", \"trigger_on_history_size_chars\": 200000, \"trigger_on_history_size_chars_when_cache_expiring\": 140000}"
  - enableCommitIndexing: false to true
  - maxCommitsToIndex: 0 to 10000
  - retryChatStreamTimeouts: false to true
  - remoteAgentsResumeHintAvailableTtlDays: 0 to 21
2025-07-25 18:25:14.961 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    c:\Users\<USER>\Desktop\augment_code-vip (explicit) at 6/30/2025, 4:07:26 PM
2025-07-25 18:25:14.961 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [false]
2025-07-25 18:25:14.961 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-07-25 18:25:14.961 [info] 'SyncingPermissionTracker' Permission to sync folder c:\Users\<USER>\Desktop\augment_code-vip granted at 6/30/2025, 4:07:26 PM; type = explicit
2025-07-25 18:25:14.961 [info] 'WorkspaceManager' Adding workspace folder augment_code-vip; folderRoot = c:\Users\<USER>\Desktop\augment_code-vip; syncingPermission = granted
2025-07-25 18:25:14.961 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    c:\Users\<USER>\Desktop\augment_code-vip (explicit) at 6/30/2025, 4:07:26 PM
2025-07-25 18:25:15.330 [info] 'AugmentExtension' Git tracking disabled by enableAgentGitTracker feature flag
2025-07-25 18:25:15.330 [info] 'MemoryWebviewMessageHandler' Memory webview message handler initialized
2025-07-25 18:25:15.330 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-07-25 18:25:15.330 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-07-25 18:25:15.330 [info] 'HotKeyHints' HotKeyHints initialized
2025-07-25 18:25:15.348 [info] 'AugmentExtension' Git tracking disabled by enableAgentGitTracker feature flag
2025-07-25 18:25:15.349 [info] 'ToolsModel' Loaded saved chat mode: AGENT
2025-07-25 18:25:15.499 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-07-25 18:25:15.500 [info] 'ToolsModel' Host: localToolHost (10 tools: 178 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + git-commit-retrieval
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-25 18:25:17.286 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 2528 msec late.
2025-07-25 18:25:17.882 [error] 'AugmentExtension' Dropping error report "agents/list-remote-tools call failed with APIStatus unknown" due to error: Canceled
