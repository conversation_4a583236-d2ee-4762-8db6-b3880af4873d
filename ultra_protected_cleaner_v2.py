import os
import sys
import base64
import zlib
import random
import hashlib
import sqlite3
import winreg
import shutil
import time
import json
from datetime import datetime

# Multi-layer protection system
def _0xa1b2c3d4():
    """Anti-analysis layer 1"""
    if sys.gettrace() is not None:
        os._exit(random.randint(1, 255))
    
    _bad_env = ['ida', 'olly', 'x64dbg', 'ghidra', 'radare', 'immunity']
    if any(x in os.environ.get('PATH', '').lower() for x in _bad_env):
        os._exit(random.randint(1, 255))

def _0xe5f6a7b8():
    """Anti-analysis layer 2"""
    try:
        import psutil
        _procs = [p.name().lower() for p in psutil.process_iter()]
        _analysis_tools = [
            'ollydbg.exe', 'ida.exe', 'ida64.exe', 'x32dbg.exe', 'x64dbg.exe',
            'cheatengine.exe', 'processhacker.exe', 'procmon.exe', 'wireshark.exe',
            'fiddler.exe', 'regshot.exe', 'apimonitor.exe'
        ]
        if any(tool in _procs for tool in _analysis_tools):
            os._exit(random.randint(1, 255))
    except:
        pass

def _0xc9d8e7f6():
    """Timing-based detection"""
    _start = time.time()
    for i in range(5000):
        _ = hashlib.sha256(str(i).encode()).hexdigest()
    if time.time() - _start > 0.5:
        os._exit(random.randint(1, 255))

# Encrypted strings and data
_0xSTRINGS = {
    's1': base64.b64decode(b'QXVnbWVudCBDbGVhbmVy').decode(),
    's2': base64.b64decode(b'U2Nhbm5pbmcgZm9yIEF1Z21lbnQ=').decode(),
    's3': base64.b64decode(b'Q2xlYW5pbmcgY29tcGxldGVk').decode(),
    's4': ''.join([chr(x^23) for x in [118, 126, 115, 116, 109, 46, 118, 126, 99, 100, 98]]),  # state.vscdb
    's5': bytes([x^42 for x in [139, 139, 141, 135, 143, 132, 138]]).decode(),  # augment
}

class _0xCLEANER:
    """Ultra-obfuscated Augment cleaner"""
    
    def __init__(self):
        _0xa1b2c3d4()
        _0xe5f6a7b8()
        _0xc9d8e7f6()
        
        self._0xfindings = {}
        self._0xcleaned = 0
        self._0xbackup_dir = None
        
        # Additional protection
        self._0xcheck_environment()
    
    def _0xcheck_environment(self):
        """Advanced environment checks"""
        # Check for VM indicators
        try:
            import wmi
            c = wmi.WMI()
            for system in c.Win32_ComputerSystem():
                _vm_indicators = ['virtualbox', 'vmware', 'qemu', 'xen', 'hyper-v']
                if any(vm in system.Model.lower() for vm in _vm_indicators):
                    os._exit(random.randint(1, 255))
        except:
            pass
        
        # Check registry for analysis tools
        try:
            _reg_paths = [
                (winreg.HKEY_LOCAL_MACHINE, r'SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall'),
                (winreg.HKEY_CURRENT_USER, r'SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall')
            ]
            
            for hkey, path in _reg_paths:
                try:
                    with winreg.OpenKey(hkey, path) as key:
                        i = 0
                        while True:
                            try:
                                subkey = winreg.EnumKey(key, i)
                                _bad_software = ['ida', 'olly', 'x64dbg', 'cheat engine', 'process hacker']
                                if any(bad in subkey.lower() for bad in _bad_software):
                                    os._exit(random.randint(1, 255))
                                i += 1
                            except WindowsError:
                                break
                except:
                    continue
        except:
            pass
    
    def _0xscan(self):
        """Obfuscated scanning function"""
        print(_0xSTRINGS['s2'])
        
        # Create obfuscated backup directory
        _timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        self._0xbackup_dir = f"{''.join([chr(x) for x in [98, 97, 99, 107, 117, 112]])}_{_timestamp}"
        os.makedirs(self._0xbackup_dir, exist_ok=True)
        
        # Scan extensions with obfuscation
        self._0xscan_extensions()
        
        # Scan databases with obfuscation
        self._0xscan_databases()
        
        # Scan system data
        self._0xscan_system_data()
        
        return self._0xgenerate_report()
    
    def _0xscan_extensions(self):
        """Scan for extensions with heavy obfuscation"""
        _0xa1b2c3d4()  # Re-check during execution
        
        _vscode_paths = [
            (os.path.expandvars(''.join([chr(x) for x in [37, 65, 80, 80, 68, 65, 84, 65, 37, 92, 67, 111, 100, 101]])), 
             ''.join([chr(x) for x in [86, 83, 67, 111, 100, 101]])),
            (os.path.expandvars(''.join([chr(x) for x in [37, 65, 80, 80, 68, 65, 84, 65, 37, 92, 67, 117, 114, 115, 111, 114]])), 
             ''.join([chr(x) for x in [67, 117, 114, 115, 111, 114]]))
        ]
        
        self._0xfindings['extensions'] = []
        
        for base_path, ide_name in _vscode_paths:
            _ext_dir = os.path.join(base_path, ''.join([chr(x) for x in [85, 115, 101, 114]]), 
                                   ''.join([chr(x) for x in [101, 120, 116, 101, 110, 115, 105, 111, 110, 115]]))
            
            if not os.path.exists(_ext_dir):
                continue
            
            for item in os.listdir(_ext_dir):
                # Obfuscated pattern matching
                _patterns = [
                    ''.join([chr(x^42) for x in [139, 139, 141, 135, 143, 132, 138]]),  # augment
                    ''.join([chr(x^17) for x in [112, 116, 126, 124, 112, 125, 113, 98, 110, 125, 112]])  # augmentcode
                ]
                
                if any(p in item.lower() for p in _patterns):
                    _ext_path = os.path.join(_ext_dir, item)
                    _version = self._0xextract_version(item)
                    
                    self._0xfindings['extensions'].append({
                        'ide': ide_name,
                        'name': item,
                        'path': _ext_path,
                        'version': _version,
                        'size': self._0xget_size(_ext_path)
                    })
    
    def _0xscan_databases(self):
        """Scan databases with obfuscation"""
        _0xe5f6a7b8()  # Re-check
        
        self._0xfindings['databases'] = []
        
        _db_paths = [
            os.path.expandvars(''.join([chr(x) for x in [37, 65, 80, 80, 68, 65, 84, 65, 37, 92, 67, 111, 100, 101, 92, 85, 115, 101, 114, 92, 103, 108, 111, 98, 97, 108, 83, 116, 111, 114, 97, 103, 101]])),
            os.path.expandvars(''.join([chr(x) for x in [37, 65, 80, 80, 68, 65, 84, 65, 37, 92, 67, 117, 114, 115, 111, 114, 92, 85, 115, 101, 114, 92, 103, 108, 111, 98, 97, 108, 83, 116, 111, 114, 97, 103, 101]]))
        ]
        
        for db_path in _db_paths:
            _state_db = os.path.join(db_path, _0xSTRINGS['s4'])
            if not os.path.exists(_state_db):
                continue
            
            try:
                _conn = sqlite3.connect(_state_db)
                _cur = _conn.cursor()
                
                # Obfuscated pattern search
                _username_pattern = ''.join([chr(x^13) for x in [126, 126, 124, 120, 122, 119, 125]])
                _computer_pattern = ''.join([chr(x^7) for x in [98, 106, 104, 113, 114, 113, 108, 125]])

                _patterns = [
                    f"%{_0xSTRINGS['s5']}%",
                    f"%{_username_pattern}%",  # username
                    f"%{_computer_pattern}%"   # computer
                ]
                
                _entries = []
                for pattern in _patterns:
                    _cur.execute(''.join([chr(x) for x in [83, 69, 76, 69, 67, 84, 32, 107, 101, 121, 44, 32, 118, 97, 108, 117, 101, 32, 70, 82, 79, 77, 32, 73, 116, 101, 109, 84, 97, 98, 108, 101, 32, 87, 72, 69, 82, 69, 32, 76, 79, 87, 69, 82, 40, 107, 101, 121, 41, 32, 76, 73, 75, 69, 32, 76, 79, 87, 69, 82, 40, 63, 41]]), (pattern,))
                    _results = _cur.fetchall()
                    _entries.extend(_results)
                
                if _entries:
                    self._0xfindings['databases'].append({
                        'path': _state_db,
                        'entries': len(_entries),
                        'data': _entries[:3]  # Sample data
                    })
                
                _conn.close()
                
            except Exception:
                pass
    
    def _0xscan_system_data(self):
        """Scan for system fingerprinting data"""
        _0xc9d8e7f6()  # Re-check
        
        self._0xfindings['system_data'] = []
        
        # Obfuscated system paths
        _sys_paths = [
            os.path.expandvars(''.join([chr(x) for x in [37, 76, 79, 67, 65, 76, 65, 80, 80, 68, 65, 84, 65, 37, 92, 65, 117, 103, 109, 101, 110, 116]])),
            os.path.expandvars(''.join([chr(x) for x in [37, 65, 80, 80, 68, 65, 84, 65, 37, 92, 65, 117, 103, 109, 101, 110, 116]])),
            os.path.expandvars(''.join([chr(x) for x in [37, 84, 69, 77, 80, 37, 92, 65, 117, 103, 109, 101, 110, 116]]))
        ]
        
        for path in _sys_paths:
            if os.path.exists(path):
                for root, dirs, files in os.walk(path):
                    for file in files:
                        _file_path = os.path.join(root, file)
                        self._0xfindings['system_data'].append({
                            'type': 'system_file',
                            'path': _file_path,
                            'size': os.path.getsize(_file_path) if os.path.exists(_file_path) else 0
                        })
    
    def _0xextract_version(self, name):
        """Extract version with obfuscation"""
        import re
        _pattern = ''.join([chr(x) for x in [40, 92, 100, 43, 92, 46, 92, 100, 43, 92, 46, 92, 100, 43, 41]])
        _match = re.search(_pattern, name)
        return _match.group(1) if _match else ''.join([chr(x) for x in [48, 46, 48, 46, 48]])
    
    def _0xget_size(self, path):
        """Get folder size with obfuscation"""
        try:
            _total = 0
            for root, dirs, files in os.walk(path):
                for file in files:
                    _fp = os.path.join(root, file)
                    if os.path.exists(_fp):
                        _total += os.path.getsize(_fp)
            return round(_total / (1024 * 1024), 2)
        except:
            return 0
    
    def _0xgenerate_report(self):
        """Generate obfuscated report"""
        _total = sum(len(self._0xfindings.get(k, [])) for k in self._0xfindings)
        
        print(f"\n{base64.b64decode(b'UmVwb3J0').decode()}: {_total} {''.join([chr(x) for x in [105, 116, 101, 109, 115]])}")
        
        if _total > 0:
            print(f"{base64.b64decode(b'RXh0ZW5zaW9ucw==').decode()}: {len(self._0xfindings.get('extensions', []))}")
            print(f"{base64.b64decode(b'RGF0YWJhc2Vz').decode()}: {len(self._0xfindings.get('databases', []))}")
            print(f"{base64.b64decode(b'U3lzdGVtIERhdGE=').decode()}: {len(self._0xfindings.get('system_data', []))}")
        
        return _total > 0
    
    def _0xclean(self):
        """Ultra-obfuscated cleaning function"""
        if not any(self._0xfindings.values()):
            print(base64.b64decode(b'Tm90aGluZyB0byBjbGVhbg==').decode())
            return 0
        
        print(f"\n{base64.b64decode(b'U3RhcnRpbmcgY2xlYW51cA==').decode()}...")
        
        # Clean extensions
        for ext in self._0xfindings.get('extensions', []):
            self._0xclean_extension(ext)
        
        # Clean databases
        for db in self._0xfindings.get('databases', []):
            self._0xclean_database(db)
        
        # Clean system data
        for sys_item in self._0xfindings.get('system_data', []):
            self._0xclean_system_item(sys_item)
        
        print(f"\n{_0xSTRINGS['s3']}: {self._0xcleaned} {''.join([chr(x) for x in [105, 116, 101, 109, 115]])}")
        return self._0xcleaned
    
    def _0xclean_extension(self, ext):
        """Clean extension with backup"""
        try:
            _path = ext['path']
            if os.path.exists(_path):
                _backup = os.path.join(self._0xbackup_dir, f"ext_{ext['name']}")
                shutil.copytree(_path, _backup)
                shutil.rmtree(_path)
                self._0xcleaned += 1
        except:
            pass
    
    def _0xclean_database(self, db):
        """Clean database with backup"""
        try:
            _path = db['path']
            if os.path.exists(_path):
                _backup = os.path.join(self._0xbackup_dir, f"db_{os.path.basename(_path)}")
                shutil.copy2(_path, _backup)
                
                _conn = sqlite3.connect(_path)
                _cur = _conn.cursor()
                
                # Remove obfuscated patterns
                _patterns = [f"%{_0xSTRINGS['s5']}%"]
                for pattern in _patterns:
                    _cur.execute(''.join([chr(x) for x in [68, 69, 76, 69, 84, 69, 32, 70, 82, 79, 77, 32, 73, 116, 101, 109, 84, 97, 98, 108, 101, 32, 87, 72, 69, 82, 69, 32, 76, 79, 87, 69, 82, 40, 107, 101, 121, 41, 32, 76, 73, 75, 69, 32, 76, 79, 87, 69, 82, 40, 63, 41]]), (pattern,))
                
                _conn.commit()
                _conn.close()
                self._0xcleaned += 1
        except:
            pass
    
    def _0xclean_system_item(self, item):
        """Clean system item with backup"""
        try:
            _path = item['path']
            if os.path.exists(_path):
                _backup = os.path.join(self._0xbackup_dir, f"sys_{os.path.basename(_path)}")
                if os.path.isfile(_path):
                    shutil.copy2(_path, _backup)
                    os.remove(_path)
                else:
                    shutil.copytree(_path, _backup)
                    shutil.rmtree(_path)
                self._0xcleaned += 1
        except:
            pass

def _0xmain():
    """Ultra-obfuscated main function"""
    _0xa1b2c3d4()
    _0xe5f6a7b8()
    
    print(_0xSTRINGS['s1'])
    print(''.join([chr(x) for x in [61] * 40]))
    
    _cleaner = _0xCLEANER()
    
    try:
        _found = _cleaner._0xscan()
        
        if not _found:
            print(f"\n{base64.b64decode(b'U3lzdGVtIGNsZWFu').decode()}")
            return
        
        _response = input(f"\n{base64.b64decode(b'UHJvY2VlZCB3aXRoIGNsZWFudXA/').decode()} (y/n): ").strip().lower()
        
        if _response in ['y', 'yes']:
            _cleaned = _cleaner._0xclean()
            print(f"\n{base64.b64decode(b'Q2xlYW51cCBjb21wbGV0ZWQ=').decode()}: {_cleaned}")
        else:
            print(f"\n{base64.b64decode(b'Q2FuY2VsbGVk').decode()}")
    
    except Exception:
        os._exit(random.randint(1, 255))
    
    finally:
        input(f"\n{base64.b64decode(b'UHJlc3MgRW50ZXI=').decode()}...")

if __name__ == "__main__":
    try:
        _0xmain()
    except:
        os._exit(random.randint(1, 255))
