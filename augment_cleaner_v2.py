import os
import sqlite3
import shutil
import json
import winreg
import subprocess
import sys
import logging
import hashlib
import random
import string
import threading
import time
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum

class ScanType(Enum):
    """Types of scans available"""
    EXTENSIONS = "extensions"
    DATABASES = "databases"
    PERSONAL_DATA = "personal_data"
    SYSTEM_FINGERPRINTS = "system_fingerprints"
    NETWORK_TRACES = "network_traces"
    CLOUD_DATA = "cloud_data"
    AI_TRAINING_DATA = "ai_training_data"
    REGISTRY_ENTRIES = "registry_entries"
    HARDWARE_FINGERPRINTS = "hardware_fingerprints"
    BROWSER_FINGERPRINTS = "browser_fingerprints"
    PROCESS_MONITORING = "process_monitoring"
    FILE_SYSTEM_TRACKING = "file_system_tracking"
    NETWORK_FINGERPRINTS = "network_fingerprints"
    CRYPTO_FINGERPRINTS = "crypto_fingerprints"
    HIDDEN_TELEMETRY = "hidden_telemetry"
    STEGANOGRAPHIC_DATA = "steganographic_data"
    MACHINE_GUID = "machine_guid"
    DISK_SERIAL = "disk_serial"
    BIOS_FINGERPRINTS = "bios_fingerprints"
    TIMEZONE_FINGERPRINTS = "timezone_fingerprints"
    FONT_FINGERPRINTS = "font_fingerprints"
    INSTALLED_SOFTWARE = "installed_software"
    ENVIRONMENT_VARIABLES = "environment_variables"
    USER_BEHAVIOR_PATTERNS = "user_behavior_patterns"
    CLIPBOARD_MONITORING = "clipboard_monitoring"
    SCREEN_FINGERPRINTS = "screen_fingerprints"

class CleaningMode(Enum):
    """Cleaning modes"""
    SAFE = "safe"           # Backup and remove
    SECURE = "secure"       # Secure deletion
    SPOOF = "spoof"         # Replace with fake data

@dataclass
class ScanResult:
    """Container for scan results"""
    scan_type: ScanType
    path: str
    details: Dict[str, Any] = field(default_factory=dict)
    risk_level: str = "medium"
    size_bytes: int = 0

@dataclass
class CleaningResult:
    """Container for cleaning results"""
    success: bool
    item_path: str
    backup_path: Optional[str] = None
    error_message: Optional[str] = None
    items_cleaned: int = 0

class AugmentCleanerV2:
    """Enhanced cleaner for newer Augment versions (0.492.2+)"""

    def __init__(self, cleaning_mode: CleaningMode = CleaningMode.SAFE, verbose: bool = True):
        """Initialize the cleaner with configuration options"""
        self.cleaning_mode = cleaning_mode
        self.verbose = verbose

        # Initialize findings with typed structure - supports both old and new formats
        self.findings: Dict[str, List] = {
            scan_type.value: [] for scan_type in ScanType
        }

        # Statistics
        self.cleaned_items = 0
        self.total_scanned = 0
        self.errors_encountered = 0

        # Paths and directories
        self.backup_dir: Optional[Path] = None
        self.log_file: Optional[Path] = None

        # Setup logging
        self._setup_logging()

        # Progress tracking
        self.current_operation = "Initializing"
        self.progress_callback = None

        # Check for admin rights
        if not self._is_admin():
            print("\n⚠️ This tool requires administrator privileges for full cleaning.")
            print("   Please restart this script as Administrator (right-click and select 'Run as administrator').")
            self.logger.warning("Not running as administrator. Some operations may fail.")

        # Check and close running IDEs
        self._close_running_ides()

        self.logger.info(f"AugmentCleanerV2 initialized with mode: {cleaning_mode.value}")

    def deep_clean(self):
        """Deep Clean: Wipe all Augment/VSCode/AI extension data, tokens, and chat logs"""
        print("\n🚨 DEEP CLEAN MODE: Wiping all Augment/VSCode/AI extension data, tokens, and chat logs...")
        self.logger.info("Starting DEEP CLEAN mode")

        # List of critical locations to wipe
        wipe_paths = [
            # Augment and extension folders
            os.path.expandvars(r"%APPDATA%\\Augment"),
            os.path.expandvars(r"%LOCALAPPDATA%\\Augment"),
            os.path.expandvars(r"%TEMP%\\Augment"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\globalStorage"),
            os.path.expandvars(r"%APPDATA%\\Code - Insiders\\User\\globalStorage"),
            os.path.expandvars(r"%APPDATA%\\Cursor\\User\\globalStorage"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\workspaceStorage"),
            os.path.expandvars(r"%APPDATA%\\Code - Insiders\\User\\workspaceStorage"),
            os.path.expandvars(r"%APPDATA%\\Cursor\\User\\workspaceStorage"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\logs"),
            os.path.expandvars(r"%APPDATA%\\Code - Insiders\\User\\logs"),
            os.path.expandvars(r"%APPDATA%\\Cursor\\User\\logs"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\settings.json"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\keybindings.json"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\snippets"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\storage.json"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\globalStorage\\state.vscdb"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\globalStorage\\state.vscdb.backup"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\globalStorage\\telemetry"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\globalStorage\\machine_id"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\globalStorage\\cpu_signature"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\globalStorage\\browser_fingerprint"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\globalStorage\\system_profile"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\globalStorage\\hardware_profile"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\globalStorage\\device_fingerprint"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\globalStorage\\chat"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\globalStorage\\tokens"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\globalStorage\\cache"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\globalStorage\\augment"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\globalStorage\\augmentcode"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\globalStorage\\augment-ai"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\globalStorage\\augment*"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\globalStorage\\*augment*"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\globalStorage\\*token*"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\globalStorage\\*chat*"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\globalStorage\\*cache*"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\globalStorage\\*history*"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\globalStorage\\*log*"),
            os.path.expandvars(r"%APPDATA%\\Code\\User\\globalStorage\\*message*"),
        ]

        deleted = 0
        for path in wipe_paths:
            if os.path.exists(path):
                try:
                    if os.path.isdir(path):
                        shutil.rmtree(path, ignore_errors=True)
                        print(f"   🗑️ Deleted directory: {path}")
                        self.logger.info(f"Deleted directory: {path}")
                    else:
                        os.remove(path)
                        print(f"   🗑️ Deleted file: {path}")
                        self.logger.info(f"Deleted file: {path}")
                    deleted += 1
                except Exception as e:
                    print(f"   ❌ Could not delete {path}: {str(e)}")
                    self.logger.error(f"Could not delete {path}: {str(e)}")
        print(f"\n✅ Deep Clean complete. {deleted} items deleted.")
        self.logger.info(f"Deep Clean complete. {deleted} items deleted.")
    def _is_admin(self) -> bool:
        """Check if running as administrator (Windows only)"""
        try:
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin() != 0
        except Exception:
            return False

    def _close_running_ides(self) -> None:
        """Detect and automatically close running IDEs that may lock files"""
        import psutil
        ide_processes = [
            'Code.exe', 'Code - Insiders.exe', 'Cursor.exe', 'VSCodium.exe', 'code.exe', 'cursor.exe'
        ]
        running_ides = []
        for proc in psutil.process_iter(['name']):
            try:
                if proc.info['name'] in ide_processes:
                    running_ides.append(proc)
            except Exception:
                continue
        if running_ides:
            print("\n⚠️ The following IDEs are currently running and will be closed automatically:")
            for proc in running_ides:
                print(f"   - {proc.info['name']} (PID: {proc.pid})")
            self.logger.warning("Automatically closing IDEs: " + ', '.join([p.info['name'] for p in running_ides]))
            for proc in running_ides:
                try:
                    proc.terminate()
                    proc.wait(timeout=10)
                    print(f"   ✅ Terminated {proc.info['name']} (PID: {proc.pid})")
                    self.logger.info(f"Terminated {proc.info['name']} (PID: {proc.pid})")
                except Exception:
                    print(f"   ❌ Could not terminate {proc.info['name']} (PID: {proc.pid})")
                    self.logger.error(f"Could not terminate {proc.info['name']} (PID: {proc.pid})")

    def _setup_logging(self) -> None:
        """Setup logging configuration"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file = log_dir / f"augment_cleaner_{timestamp}.log"

        # Configure logger
        self.logger = logging.getLogger("AugmentCleaner")
        self.logger.setLevel(logging.DEBUG if self.verbose else logging.INFO)

        # File handler
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)

        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO if self.verbose else logging.WARNING)

        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)

    def set_progress_callback(self, callback) -> None:
        """Set a callback function for progress updates"""
        self.progress_callback = callback

    def _update_progress(self, operation: str, progress: float = 0.0) -> None:
        """Update progress and notify callback if set"""
        self.current_operation = operation
        if self.progress_callback:
            self.progress_callback(operation, progress)
        if self.verbose:
            print(f"🔄 {operation}")

    def _safe_path_operation(self, operation, path: str, *args, **kwargs) -> bool:
        """Safely perform path operations with error handling"""
        try:
            if not os.path.exists(path):
                self.logger.warning(f"Path does not exist: {path}")
                return False

            result = operation(path, *args, **kwargs)
            self.logger.debug(f"Successfully performed operation on: {path}")
            return result if result is not None else True

        except PermissionError:
            self.logger.error(f"Permission denied accessing: {path}")
            self.errors_encountered += 1
            return False
        except Exception as e:
            self.logger.error(f"Error with path {path}: {str(e)}")
            self.errors_encountered += 1
            return False

    def _create_backup_structure(self) -> Path:
        """Create backup directory structure"""
        if self.backup_dir is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.backup_dir = Path(f"augment_backup_{timestamp}")
            self.backup_dir.mkdir(exist_ok=True)

            # Create subdirectories for different types
            for scan_type in ScanType:
                (self.backup_dir / scan_type.value).mkdir(exist_ok=True)

            self.logger.info(f"Created backup directory: {self.backup_dir}")

        return self.backup_dir

    def _generate_fake_data(self, data_type: str) -> str:
        """Generate realistic fake data for spoofing mode - ENHANCED VERSION"""

        # Realistic fake data generators that match real system patterns
        fake_generators = {
            'username': lambda: random.choice(['john', 'mike', 'sarah', 'david', 'alex', 'chris', 'emma', 'lisa']) + str(random.randint(10, 99)),
            'computer': lambda: random.choice(['DESKTOP', 'LAPTOP', 'PC']) + '-' + ''.join(random.choices(string.ascii_uppercase + string.digits, k=6)),
            'email': lambda: f"{random.choice(['user', 'admin', 'test', 'demo'])}{random.randint(100, 999)}@{random.choice(['gmail.com', 'outlook.com', 'yahoo.com'])}",
            'uuid': lambda: str(hashlib.md5(os.urandom(16)).hexdigest()),
            'path': lambda: f"C:\\Users\\<USER>\\{random.choice(['Documents', 'Desktop', 'Downloads'])}",
            'machine_guid': lambda: '{' + '-'.join([
                ''.join(random.choices('0123456789ABCDEF', k=8)),
                ''.join(random.choices('0123456789ABCDEF', k=4)),
                ''.join(random.choices('0123456789ABCDEF', k=4)),
                ''.join(random.choices('0123456789ABCDEF', k=4)),
                ''.join(random.choices('0123456789ABCDEF', k=12))
            ]) + '}',
            'cpu_signature': lambda: f"Intel(R) Core(TM) i{random.choice([3,5,7])}-{random.randint(8000, 12000)}U CPU @ {random.uniform(1.6, 3.2):.1f}GHz",
            'memory_size': lambda: str(random.choice([4, 8, 16, 32]) * 1024 * 1024 * 1024),  # GB in bytes
            'disk_serial': lambda: ''.join(random.choices(string.ascii_uppercase + string.digits, k=20)),
            'mac_address': lambda: ':'.join(['%02x' % random.randint(0, 255) for _ in range(6)]),
            'ip_address': lambda: '.'.join([str(random.randint(1, 254)) for _ in range(4)]),
            'timezone': lambda: random.choice(['UTC-08:00', 'UTC-05:00', 'UTC+00:00', 'UTC+01:00', 'UTC+08:00']),
            'screen_resolution': lambda: random.choice(['1920x1080', '1366x768', '2560x1440', '3840x2160']),
            'browser_agent': lambda: f"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 Chrome/{random.randint(90, 120)}.0.0.0",
        }

        return fake_generators.get(data_type, lambda: "REDACTED")()

    def _secure_delete_file(self, file_path: str, passes: int = 3) -> bool:
        """Securely delete a file by overwriting it multiple times"""
        try:
            if not os.path.exists(file_path):
                return True

            file_size = os.path.getsize(file_path)

            with open(file_path, 'r+b') as file:
                for pass_num in range(passes):
                    file.seek(0)
                    # Overwrite with random data
                    file.write(os.urandom(file_size))
                    file.flush()
                    os.fsync(file.fileno())  # Force write to disk

                    # Overwrite with zeros
                    file.seek(0)
                    file.write(b'\x00' * file_size)
                    file.flush()
                    os.fsync(file.fileno())

                    self.logger.debug(f"Secure delete pass {pass_num + 1}/{passes} for {file_path}")

            # Finally delete the file
            os.remove(file_path)
            self.logger.info(f"Securely deleted: {file_path}")
            return True

        except Exception as e:
            self.logger.error(f"Secure deletion failed for {file_path}: {str(e)}")
            return False

    def _spoof_database_data(self, db_path: str) -> bool:
        """Replace sensitive data in database with fake data"""
        try:
            conn = sqlite3.connect(db_path)
            cur = conn.cursor()

            # Get all tables
            cur.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cur.fetchall()

            spoofed_entries = 0

            for table_name, in tables:
                try:
                    # Get column info
                    cur.execute(f"PRAGMA table_info({table_name})")
                    columns = cur.fetchall()

                    for col_info in columns:
                        col_name = col_info[1]

                        # Identify sensitive columns and spoof them
                        if any(keyword in col_name.lower() for keyword in
                               ['user', 'name', 'email', 'path', 'identity', 'machine']):

                            fake_data = self._generate_fake_data('username')
                            cur.execute(f"UPDATE {table_name} SET {col_name} = ? WHERE {col_name} IS NOT NULL",
                                      (fake_data,))
                            spoofed_entries += cur.rowcount

                except Exception as e:
                    self.logger.warning(f"Could not spoof table {table_name}: {str(e)}")
                    continue

            conn.commit()
            conn.close()

            self.logger.info(f"Spoofed {spoofed_entries} database entries in {db_path}")
            return True

        except Exception as e:
            self.logger.error(f"Database spoofing failed for {db_path}: {str(e)}")
            return False

    def _create_decoy_files(self, target_dir: str) -> bool:
        """Create decoy files to confuse data recovery attempts"""
        try:
            decoy_dir = Path(target_dir) / "decoy_data"
            decoy_dir.mkdir(exist_ok=True)

            # Create multiple decoy files with fake data
            for i in range(10):
                decoy_file = decoy_dir / f"fake_data_{i}.json"
                fake_data = {
                    'user': self._generate_fake_data('username'),
                    'computer': self._generate_fake_data('computer'),
                    'email': self._generate_fake_data('email'),
                    'timestamp': datetime.now().isoformat(),
                    'fake': True
                }

                with open(decoy_file, 'w') as f:
                    json.dump(fake_data, f, indent=2)

            self.logger.info(f"Created decoy files in {decoy_dir}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to create decoy files: {str(e)}")
            return False

    def scan_for_newer_augment(self) -> bool:
        """Comprehensive scan for newer Augment versions and their data"""
        self.logger.info("Starting comprehensive Augment scan")
        print("🔍 Enhanced Augment Scanner v2.0 - Targeting newer versions")
        print("=" * 60)

        try:
            # Create backup directory structure
            self._create_backup_structure()

            # Define scan operations with progress weights - COMPREHENSIVE FINGERPRINT DETECTION
            scan_operations = [
                (self.scan_extensions, "Scanning extensions", 8),
                (self.scan_databases_deep, "Deep scanning databases", 12),
                (self.scan_personal_data, "Scanning personal data", 8),
                (self.scan_system_fingerprints, "Scanning system fingerprints", 8),
                (self.scan_hardware_fingerprints, "🔍 DEEP HARDWARE FINGERPRINTING", 12),
                (self.scan_browser_fingerprints, "🌐 BROWSER FINGERPRINTING", 8),
                (self.scan_process_monitoring, "👁️ PROCESS MONITORING", 8),
                (self.scan_file_system_tracking, "📁 FILE SYSTEM TRACKING", 8),
                (self.scan_network_fingerprints, "🌐 NETWORK FINGERPRINTING", 8),
                (self.scan_crypto_fingerprints, "🔐 CRYPTO FINGERPRINTING", 8),
                (self.scan_hidden_telemetry, "📡 HIDDEN TELEMETRY", 8),
                (self.scan_steganographic_data, "🕵️ STEGANOGRAPHIC DATA", 4),
                (self.scan_machine_guid, "🔍 MACHINE GUID FINGERPRINTS", 6),
                (self.scan_disk_serial, "💾 DISK SERIAL FINGERPRINTS", 6),
                (self.scan_bios_fingerprints, "⚡ BIOS FINGERPRINTS", 6),
                (self.scan_timezone_fingerprints, "🌍 TIMEZONE FINGERPRINTS", 4),
                (self.scan_font_fingerprints, "🔤 FONT FINGERPRINTS", 4),
                (self.scan_installed_software, "📦 SOFTWARE FINGERPRINTS", 6),
                (self.scan_environment_variables, "🌐 ENV VAR FINGERPRINTS", 4),
                (self.scan_user_behavior_patterns, "👤 BEHAVIOR FINGERPRINTS", 6),
                (self.scan_clipboard_monitoring, "📋 CLIPBOARD MONITORING", 4),
                (self.scan_screen_fingerprints, "🖥️ SCREEN FINGERPRINTS", 4),
                (self.scan_advanced_system_info, "🔬 ADVANCED SYSTEM INFO", 8),
                (self.scan_cloud_data, "Scanning cloud data", 4),
                (self.scan_ai_training_data, "Scanning AI training data", 4),
                (self.scan_registry_deep, "Deep scanning registry", 4),
                (self.scan_network_traces, "Scanning network traces", 4)
            ]

            total_progress = 0
            for scan_func, description, weight in scan_operations:
                self._update_progress(description, total_progress)

                try:
                    scan_func()
                    total_progress += weight
                    self.logger.debug(f"Completed: {description}")
                except Exception as e:
                    self.logger.error(f"Error in {description}: {str(e)}")
                    self.errors_encountered += 1
                    continue

            self._update_progress("Generating report", 100)
            return self.generate_findings_report()

        except Exception as e:
            self.logger.error(f"Critical error during scan: {str(e)}")
            print(f"❌ Critical error: {str(e)}")
            return False
    
    def scan_extensions(self) -> None:
        """Scan for Augment extensions with version detection"""
        self.logger.info("Starting extension scan")
        print("\n📦 Scanning for Augment extensions...")

        vscode_paths = [
            (os.path.expandvars(r"%APPDATA%\Code"), "VSCode"),
            (os.path.expandvars(r"%APPDATA%\Code - Insiders"), "VSCode Insiders"),
            (os.path.expandvars(r"%APPDATA%\Cursor"), "Cursor"),
            (os.path.expanduser("~/.vscode"), "VSCode (User)"),
        ]

        extensions_found = 0

        for base_path, ide_name in vscode_paths:
            extensions_dir = Path(base_path) / "User" / "extensions"

            if not self._safe_path_operation(os.path.exists, str(extensions_dir)):
                self.logger.debug(f"Extensions directory not found: {extensions_dir}")
                continue

            try:
                for item in os.listdir(extensions_dir):
                    if any(pattern in item.lower() for pattern in ['augment', 'augmentcode']):
                        ext_path = extensions_dir / item
                        version = self.extract_version(item)
                        size_mb = self.get_folder_size_mb(str(ext_path))
                        is_newer = self.is_newer_version(version)

                        # Create structured result
                        scan_result = ScanResult(
                            scan_type=ScanType.EXTENSIONS,
                            path=str(ext_path),
                            details={
                                'ide': ide_name,
                                'name': item,
                                'version': version,
                                'is_newer': is_newer,
                                'size_mb': size_mb
                            },
                            risk_level="high" if is_newer else "medium",
                            size_bytes=int(size_mb * 1024 * 1024)
                        )

                        self.findings['extensions'].append(scan_result)
                        extensions_found += 1

                        print(f"   📦 Found: {item} (v{version}) in {ide_name}")
                        self.logger.info(f"Extension found: {item} v{version} in {ide_name}")

                        if is_newer:
                            print(f"       🚨 NEWER VERSION - Enhanced data collection!")
                            self.logger.warning(f"Newer version detected: {item}")

            except PermissionError:
                self.logger.error(f"Permission denied accessing: {extensions_dir}")
                self.errors_encountered += 1
            except Exception as e:
                self.logger.error(f"Error scanning {extensions_dir}: {str(e)}")
                self.errors_encountered += 1

        self.total_scanned += extensions_found
        self.logger.info(f"Extension scan completed. Found {extensions_found} extensions")
    
    def scan_databases_deep(self):
        """Deep scan of VSCode databases for personal data"""
        print("\n🗄️ Deep scanning databases for personal data...")
        
        vscode_paths = [
            os.path.expandvars(r"%APPDATA%\Code\User\globalStorage"),
            os.path.expandvars(r"%APPDATA%\Code - Insiders\User\globalStorage"),
            os.path.expandvars(r"%APPDATA%\Cursor\User\globalStorage")
        ]
        
        personal_patterns = [
            '%username%', '%user%', '%computer%', '%machine%', '%email%',
            '%identity%', '%profile%', '%account%', '%name%', '%domain%'
        ]
        
        for vscode_path in vscode_paths:
            state_db = os.path.join(vscode_path, "state.vscdb")
            if not os.path.exists(state_db):
                continue
                
            try:
                conn = sqlite3.connect(state_db)
                cur = conn.cursor()
                
                # Check for personal data in database
                personal_entries = []
                for pattern in personal_patterns:
                    cur.execute("SELECT key, value FROM ItemTable WHERE LOWER(key) LIKE ? OR LOWER(value) LIKE ?", 
                              (pattern, pattern))
                    results = cur.fetchall()
                    personal_entries.extend(results)
                
                # Check for actual username in data
                username = os.environ.get('USERNAME', '').lower()
                if username:
                    cur.execute("SELECT key, value FROM ItemTable WHERE LOWER(value) LIKE ?", 
                              (f'%{username}%',))
                    username_entries = cur.fetchall()
                    personal_entries.extend(username_entries)
                
                if personal_entries:
                    self.findings['personal_data'].append({
                        'database': state_db,
                        'entries': len(personal_entries),
                        'sample_keys': [entry[0] for entry in personal_entries[:5]],
                        'contains_username': any(username in str(entry[1]).lower() for entry in personal_entries)
                    })
                    
                    print(f"   🚨 Found {len(personal_entries)} personal data entries in {os.path.basename(state_db)}")
                    if any(username in str(entry[1]).lower() for entry in personal_entries):
                        print(f"       ⚠️ Contains your actual username: {username}")
                
                conn.close()
                
            except Exception as e:
                print(f"   ❌ Error scanning {state_db}: {str(e)}")
    
    def scan_personal_data(self):
        """Scan for personal data collection"""
        print("\n👤 Scanning for personal data collection...")
        
        # Check workspace storage for personal projects
        workspace_paths = [
            os.path.expandvars(r"%APPDATA%\Code\User\workspaceStorage"),
            os.path.expandvars(r"%APPDATA%\Code - Insiders\User\workspaceStorage"),
            os.path.expandvars(r"%APPDATA%\Cursor\User\workspaceStorage")
        ]
        
        for workspace_path in workspace_paths:
            if not os.path.exists(workspace_path):
                continue
                
            for workspace_dir in os.listdir(workspace_path):
                workspace_full = os.path.join(workspace_path, workspace_dir)
                if os.path.isdir(workspace_full):
                    # Check for Augment-related files
                    for file in os.listdir(workspace_full):
                        if 'augment' in file.lower():
                            self.findings['personal_data'].append({
                                'type': 'workspace_data',
                                'path': os.path.join(workspace_full, file),
                                'workspace': workspace_dir
                            })
                            print(f"   📁 Personal workspace data: {file}")
    
    def scan_system_fingerprints(self):
        """Scan for system fingerprinting data"""
        print("\n🖥️ Scanning for system fingerprinting data...")
        
        # Check for hardware fingerprint files
        fingerprint_locations = [
            os.path.expandvars(r"%LOCALAPPDATA%\Augment"),
            os.path.expandvars(r"%APPDATA%\Augment"),
            os.path.expandvars(r"%TEMP%\Augment")
        ]
        
        for location in fingerprint_locations:
            if os.path.exists(location):
                for root, dirs, files in os.walk(location):
                    for file in files:
                        if any(keyword in file.lower() for keyword in ['hardware', 'system', 'fingerprint', 'machine']):
                            file_path = os.path.join(root, file)
                            self.findings['system_fingerprints'].append({
                                'type': 'hardware_fingerprint',
                                'path': file_path,
                                'size': os.path.getsize(file_path)
                            })
                            print(f"   🖥️ System fingerprint: {file}")
    
    def scan_cloud_data(self):
        """Scan for cloud synchronization data"""
        print("\n☁️ Scanning for cloud synchronization data...")
        
        cloud_patterns = ['sync', 'cloud', 'remote', 'server', 'upload', 'backup']
        
        # Check VSCode logs for cloud activity
        log_paths = [
            os.path.expandvars(r"%APPDATA%\Code\logs"),
            os.path.expandvars(r"%APPDATA%\Code - Insiders\logs")
        ]
        
        for log_path in log_paths:
            if os.path.exists(log_path):
                for root, dirs, files in os.walk(log_path):
                    for file in files:
                        if file.endswith('.log'):
                            file_path = os.path.join(root, file)
                            try:
                                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                                    content = f.read()
                                    if any(pattern in content.lower() for pattern in cloud_patterns):
                                        self.findings['cloud_data'].append({
                                            'type': 'cloud_activity_log',
                                            'path': file_path,
                                            'suspicious': True
                                        })
                                        print(f"   ☁️ Cloud activity in logs: {file}")
                                        break
                            except Exception:
                                pass
    
    def scan_ai_training_data(self):
        """Scan for AI/ML training data collection"""
        print("\n🤖 Scanning for AI/ML training data...")
        
        ai_patterns = ['training', 'model', 'ml', 'ai', 'neural', 'learning']
        
        # Check extension directories for AI data
        for extension in self.findings['extensions']:
            if extension['is_newer']:
                ext_path = extension['path']
                for root, dirs, files in os.walk(ext_path):
                    for file in files:
                        if any(pattern in file.lower() for pattern in ai_patterns):
                            file_path = os.path.join(root, file)
                            self.findings['ai_training_data'].append({
                                'type': 'ai_training_file',
                                'path': file_path,
                                'extension': extension['name']
                            })
                            print(f"   🤖 AI training data: {file}")
    
    def scan_registry_deep(self):
        """Deep scan of Windows Registry for Augment data"""
        print("\n🗂️ Deep scanning Windows Registry...")
        
        try:
            registry_paths = [
                (winreg.HKEY_CURRENT_USER, r"Software"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE"),
                (winreg.HKEY_CURRENT_USER, r"Software\Microsoft\Windows\CurrentVersion\Uninstall")
            ]
            
            for hkey, path in registry_paths:
                try:
                    with winreg.OpenKey(hkey, path) as key:
                        i = 0
                        while True:
                            try:
                                subkey_name = winreg.EnumKey(key, i)
                                if 'augment' in subkey_name.lower():
                                    self.findings['registry_entries'].append({
                                        'hkey': 'HKEY_CURRENT_USER' if hkey == winreg.HKEY_CURRENT_USER else 'HKEY_LOCAL_MACHINE',
                                        'path': f"{path}\\{subkey_name}",
                                        'name': subkey_name
                                    })
                                    print(f"   🗂️ Registry entry: {subkey_name}")
                                i += 1
                            except WindowsError:
                                break
                except Exception:
                    continue
        except Exception as e:
            print(f"   ❌ Registry scan error: {str(e)}")
    
    def scan_network_traces(self):
        """Scan for network activity traces"""
        print("\n🌐 Scanning for network traces...")
        
        # Check hosts file
        hosts_file = r"C:\Windows\System32\drivers\etc\hosts"
        try:
            if os.path.exists(hosts_file):
                with open(hosts_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    if 'augment' in content.lower():
                        self.findings['network_traces'].append({
                            'type': 'hosts_file_entry',
                            'path': hosts_file
                        })
                        print("   🌐 Found Augment entries in hosts file")
        except Exception:
            pass

    def scan_hardware_fingerprints(self) -> None:
        """🔍 COMPREHENSIVE HARDWARE FINGERPRINTING DETECTION - Find ALL hardware fingerprints"""
        self.logger.info("Starting comprehensive hardware fingerprinting scan")
        print("\n🖥️ 🔍 DEEP HARDWARE FINGERPRINTING SCAN - Finding ALL fingerprints...")

        hardware_found = 0

        # 1. WMI Hardware Information Collection
        try:
            print("   🔍 Checking WMI hardware data collection...")
            # Check for hardware queries in logs/databases without importing wmi
            hardware_queries = [
                'Win32_Processor', 'Win32_BaseBoard', 'Win32_BIOS', 'Win32_ComputerSystem',
                'Win32_PhysicalMemory', 'Win32_DiskDrive', 'Win32_VideoController',
                'Win32_NetworkAdapter', 'Win32_SystemEnclosure'
            ]

            for query in hardware_queries:
                # Check if Augment has collected this data
                if self._check_hardware_data_collection(query):
                    scan_result = ScanResult(
                        scan_type=ScanType.HARDWARE_FINGERPRINTS,
                        path=f"WMI:{query}",
                        details={
                            'type': 'wmi_hardware_query',
                            'query': query,
                            'risk_level': 'critical'
                        },
                        risk_level="critical"
                    )
                    self.findings['hardware_fingerprints'].append(scan_result)
                    hardware_found += 1
                    print(f"   🚨 CRITICAL: Hardware data collected via {query}")

        except Exception as e:
            self.logger.error(f"WMI scan error: {str(e)}")

        # 2. CPU Fingerprinting Detection
        print("   🔍 Checking CPU fingerprinting...")
        cpu_fingerprint_paths = [
            os.path.expandvars(r"%TEMP%\cpu_info.dat"),
            os.path.expandvars(r"%LOCALAPPDATA%\Augment\hardware\cpu.json"),
            os.path.expandvars(r"%APPDATA%\Code\User\globalStorage\cpu_signature"),
        ]

        for path in cpu_fingerprint_paths:
            if os.path.exists(path):
                scan_result = ScanResult(
                    scan_type=ScanType.HARDWARE_FINGERPRINTS,
                    path=path,
                    details={
                        'type': 'cpu_fingerprint',
                        'size': os.path.getsize(path),
                        'risk_level': 'high'
                    },
                    risk_level="high",
                    size_bytes=os.path.getsize(path)
                )
                self.findings['hardware_fingerprints'].append(scan_result)
                hardware_found += 1
                print(f"   🚨 CPU fingerprint found: {os.path.basename(path)}")

        # 3. Memory Configuration Fingerprinting
        print("   🔍 Checking memory fingerprinting...")
        try:
            memory_patterns = ['memory', 'ram', 'dimm', 'physical_memory']
            for pattern in memory_patterns:
                memory_files = self._find_files_with_pattern(pattern, [
                    os.path.expandvars(r"%LOCALAPPDATA%\Augment"),
                    os.path.expandvars(r"%APPDATA%\Code\User\globalStorage"),
                    os.path.expandvars(r"%TEMP%")
                ])

                for file_path in memory_files:
                    scan_result = ScanResult(
                        scan_type=ScanType.HARDWARE_FINGERPRINTS,
                        path=file_path,
                        details={
                            'type': 'memory_fingerprint',
                            'pattern': pattern,
                            'risk_level': 'high'
                        },
                        risk_level="high"
                    )
                    self.findings['hardware_fingerprints'].append(scan_result)
                    hardware_found += 1
                    print(f"   🚨 Memory fingerprint: {os.path.basename(file_path)}")

        except Exception as e:
            self.logger.error(f"Memory fingerprint scan error: {str(e)}")

        self.total_scanned += hardware_found
        self.logger.info(f"Hardware fingerprint scan completed. Found {hardware_found} fingerprints")

        if hardware_found > 0:
            print(f"   🚨 TOTAL HARDWARE FINGERPRINTS FOUND: {hardware_found}")
            print("   ⚠️ Your hardware is being uniquely identified!")
        else:
            print("   ✅ No hardware fingerprints detected")

    def scan_browser_fingerprints(self) -> None:
        """🌐 BROWSER FINGERPRINTING DETECTION - Find browser-based fingerprints"""
        self.logger.info("Starting browser fingerprinting scan")
        print("\n🌐 BROWSER FINGERPRINTING SCAN - Detecting browser-based tracking...")

        browser_found = 0

        # Check for browser fingerprinting data
        browser_data_paths = [
            os.path.expandvars(r"%APPDATA%\Code\User\globalStorage\browser_fingerprint"),
            os.path.expandvars(r"%LOCALAPPDATA%\Augment\browser"),
            os.path.expandvars(r"%TEMP%\browser_data.json")
        ]

        for path in browser_data_paths:
            if os.path.exists(path):
                scan_result = ScanResult(
                    scan_type=ScanType.BROWSER_FINGERPRINTS,
                    path=path,
                    details={
                        'type': 'browser_fingerprint',
                        'risk_level': 'high'
                    },
                    risk_level="high"
                )
                self.findings['browser_fingerprints'].append(scan_result)
                browser_found += 1
                print(f"   🚨 Browser fingerprint: {os.path.basename(path)}")

        self.total_scanned += browser_found
        self.logger.info(f"Browser fingerprint scan completed. Found {browser_found} fingerprints")

    def scan_process_monitoring(self) -> None:
        """👁️ PROCESS MONITORING DETECTION - Find process tracking"""
        self.logger.info("Starting process monitoring scan")
        print("\n👁️ PROCESS MONITORING SCAN - Detecting process tracking...")

        process_found = 0

        # Check for process monitoring files
        process_patterns = ['process', 'task', 'monitor', 'running', 'executable']
        for pattern in process_patterns:
            process_files = self._find_files_with_pattern(pattern, [
                os.path.expandvars(r"%LOCALAPPDATA%\Augment"),
                os.path.expandvars(r"%APPDATA%\Code\User\globalStorage"),
                os.path.expandvars(r"%TEMP%")
            ])

            for file_path in process_files:
                scan_result = ScanResult(
                    scan_type=ScanType.PROCESS_MONITORING,
                    path=file_path,
                    details={
                        'type': 'process_monitoring',
                        'pattern': pattern,
                        'risk_level': 'high'
                    },
                    risk_level="high"
                )
                self.findings['process_monitoring'].append(scan_result)
                process_found += 1
                print(f"   🚨 Process monitoring: {os.path.basename(file_path)}")

        self.total_scanned += process_found
        self.logger.info(f"Process monitoring scan completed. Found {process_found} items")

    def scan_file_system_tracking(self) -> None:
        """📁 FILE SYSTEM TRACKING DETECTION - Find file access tracking"""
        self.logger.info("Starting file system tracking scan")
        print("\n📁 FILE SYSTEM TRACKING SCAN - Detecting file access tracking...")

        fs_found = 0

        # Check for file system tracking
        fs_patterns = ['file_access', 'directory', 'path_history', 'recent_files']
        for pattern in fs_patterns:
            fs_files = self._find_files_with_pattern(pattern, [
                os.path.expandvars(r"%LOCALAPPDATA%\Augment"),
                os.path.expandvars(r"%APPDATA%\Code\User\globalStorage"),
                os.path.expandvars(r"%TEMP%")
            ])

            for file_path in fs_files:
                scan_result = ScanResult(
                    scan_type=ScanType.FILE_SYSTEM_TRACKING,
                    path=file_path,
                    details={
                        'type': 'file_system_tracking',
                        'pattern': pattern,
                        'risk_level': 'medium'
                    },
                    risk_level="medium"
                )
                self.findings['file_system_tracking'].append(scan_result)
                fs_found += 1
                print(f"   🚨 File system tracking: {os.path.basename(file_path)}")

        self.total_scanned += fs_found
        self.logger.info(f"File system tracking scan completed. Found {fs_found} items")

    def scan_network_fingerprints(self) -> None:
        """🌐 NETWORK FINGERPRINTING DETECTION - Find network-based fingerprints"""
        self.logger.info("Starting network fingerprinting scan")
        print("\n🌐 NETWORK FINGERPRINTING SCAN - Detecting network-based tracking...")

        network_found = 0

        # Check for network fingerprinting
        network_patterns = ['network_config', 'ip_address', 'mac_address', 'network_adapter']
        for pattern in network_patterns:
            network_files = self._find_files_with_pattern(pattern, [
                os.path.expandvars(r"%LOCALAPPDATA%\Augment"),
                os.path.expandvars(r"%APPDATA%\Code\User\globalStorage"),
                os.path.expandvars(r"%TEMP%")
            ])

            for file_path in network_files:
                scan_result = ScanResult(
                    scan_type=ScanType.NETWORK_FINGERPRINTS,
                    path=file_path,
                    details={
                        'type': 'network_fingerprint',
                        'pattern': pattern,
                        'risk_level': 'high'
                    },
                    risk_level="high"
                )
                self.findings['network_fingerprints'].append(scan_result)
                network_found += 1
                print(f"   🚨 Network fingerprint: {os.path.basename(file_path)}")

        self.total_scanned += network_found
        self.logger.info(f"Network fingerprinting scan completed. Found {network_found} items")

    def scan_crypto_fingerprints(self) -> None:
        """🔐 CRYPTO FINGERPRINTING DETECTION - Find cryptographic fingerprints"""
        self.logger.info("Starting crypto fingerprinting scan")
        print("\n🔐 CRYPTO FINGERPRINTING SCAN - Detecting cryptographic tracking...")

        crypto_found = 0

        # Check for crypto fingerprinting
        crypto_patterns = ['hash', 'signature', 'certificate', 'key', 'crypto']
        for pattern in crypto_patterns:
            crypto_files = self._find_files_with_pattern(pattern, [
                os.path.expandvars(r"%LOCALAPPDATA%\Augment"),
                os.path.expandvars(r"%APPDATA%\Code\User\globalStorage"),
                os.path.expandvars(r"%TEMP%")
            ])

            for file_path in crypto_files:
                scan_result = ScanResult(
                    scan_type=ScanType.CRYPTO_FINGERPRINTS,
                    path=file_path,
                    details={
                        'type': 'crypto_fingerprint',
                        'pattern': pattern,
                        'risk_level': 'critical'
                    },
                    risk_level="critical"
                )
                self.findings['crypto_fingerprints'].append(scan_result)
                crypto_found += 1
                print(f"   🚨 CRITICAL: Crypto fingerprint: {os.path.basename(file_path)}")

        self.total_scanned += crypto_found
        self.logger.info(f"Crypto fingerprinting scan completed. Found {crypto_found} items")

    def scan_hidden_telemetry(self) -> None:
        """📡 HIDDEN TELEMETRY DETECTION - Find hidden data transmission"""
        self.logger.info("Starting hidden telemetry scan")
        print("\n📡 HIDDEN TELEMETRY SCAN - Detecting hidden data transmission...")

        telemetry_found = 0

        # Check for hidden telemetry files
        telemetry_patterns = ['telemetry', 'analytics', 'metrics', 'usage', 'stats']
        for pattern in telemetry_patterns:
            telemetry_files = self._find_files_with_pattern(pattern, [
                os.path.expandvars(r"%LOCALAPPDATA%\Augment"),
                os.path.expandvars(r"%APPDATA%\Code\User\globalStorage"),
                os.path.expandvars(r"%TEMP%"),
                os.path.expandvars(r"%APPDATA%\Code\logs")
            ])

            for file_path in telemetry_files:
                scan_result = ScanResult(
                    scan_type=ScanType.HIDDEN_TELEMETRY,
                    path=file_path,
                    details={
                        'type': 'hidden_telemetry',
                        'pattern': pattern,
                        'risk_level': 'critical'
                    },
                    risk_level="critical"
                )
                self.findings['hidden_telemetry'].append(scan_result)
                telemetry_found += 1
                print(f"   🚨 CRITICAL: Hidden telemetry: {os.path.basename(file_path)}")

        self.total_scanned += telemetry_found
        self.logger.info(f"Hidden telemetry scan completed. Found {telemetry_found} items")

    def scan_steganographic_data(self) -> None:
        """🕵️ STEGANOGRAPHIC DATA DETECTION - Find hidden data in files"""
        self.logger.info("Starting steganographic data scan")
        print("\n🕵️ STEGANOGRAPHIC DATA SCAN - Detecting hidden data in files...")

        stego_found = 0

        # Check for suspicious files that might contain hidden data
        suspicious_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.ico', '.svg']
        search_paths = [
            os.path.expandvars(r"%LOCALAPPDATA%\Augment"),
            os.path.expandvars(r"%APPDATA%\Code\User\globalStorage"),
            os.path.expandvars(r"%TEMP%")
        ]

        for search_path in search_paths:
            if os.path.exists(search_path):
                for root, _, files in os.walk(search_path):
                    for file in files:
                        if any(file.lower().endswith(ext) for ext in suspicious_extensions):
                            file_path = os.path.join(root, file)
                            # Check if file is suspiciously large for its type
                            try:
                                file_size = os.path.getsize(file_path)
                                if file_size > 1024 * 1024:  # Larger than 1MB
                                    scan_result = ScanResult(
                                        scan_type=ScanType.STEGANOGRAPHIC_DATA,
                                        path=file_path,
                                        details={
                                            'type': 'suspicious_image',
                                            'size': file_size,
                                            'risk_level': 'medium'
                                        },
                                        risk_level="medium",
                                        size_bytes=file_size
                                    )
                                    self.findings['steganographic_data'].append(scan_result)
                                    stego_found += 1
                                    print(f"   🚨 Suspicious large image: {os.path.basename(file_path)}")
                            except Exception:
                                continue

        self.total_scanned += stego_found
        self.logger.info(f"Steganographic data scan completed. Found {stego_found} items")

    def scan_machine_guid(self) -> None:
        """🔍 MACHINE GUID DETECTION - Find Windows machine GUID fingerprints"""
        self.logger.info("Starting machine GUID scan")
        print("\n🔍 MACHINE GUID SCAN - Detecting Windows machine GUID fingerprints...")

        guid_found = 0

        # Check for machine GUID in various locations
        guid_locations = [
            os.path.expandvars(r"%LOCALAPPDATA%\Augment\machine_guid"),
            os.path.expandvars(r"%APPDATA%\Code\User\globalStorage\machine_id"),
            os.path.expandvars(r"%TEMP%\machine_guid.dat"),
            os.path.expandvars(r"%APPDATA%\Code\User\globalStorage\telemetry\machine")
        ]

        for location in guid_locations:
            if os.path.exists(location):
                scan_result = ScanResult(
                    scan_type=ScanType.MACHINE_GUID,
                    path=location,
                    details={
                        'type': 'machine_guid',
                        'risk_level': 'critical'
                    },
                    risk_level="critical"
                )
                self.findings['machine_guid'].append(scan_result)
                guid_found += 1
                print(f"   🚨 CRITICAL: Machine GUID found: {os.path.basename(location)}")

        # Check registry for machine GUID collection
        try:
            import winreg
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Cryptography") as key:
                try:
                    machine_guid, _ = winreg.QueryValueEx(key, "MachineGuid")
                    # Check if this GUID appears in Augment files
                    if self._check_guid_in_files(machine_guid):
                        scan_result = ScanResult(
                            scan_type=ScanType.MACHINE_GUID,
                            path="Registry:MachineGuid",
                            details={
                                'type': 'registry_machine_guid',
                                'guid': machine_guid[:8] + "...",  # Partial for privacy
                                'risk_level': 'critical',
                                'real_guid': machine_guid  # Store for spoofing
                            },
                            risk_level="critical"
                        )
                        self.findings['machine_guid'].append(scan_result)
                        guid_found += 1
                        print(f"   🚨 CRITICAL: Machine GUID collected from registry!")
                except Exception:
                    pass
        except Exception as e:
            self.logger.error(f"Registry GUID check error: {str(e)}")

        self.total_scanned += guid_found
        self.logger.info(f"Machine GUID scan completed. Found {guid_found} items")

    def scan_disk_serial(self) -> None:
        """💾 DISK SERIAL DETECTION - Find disk serial number fingerprints"""
        self.logger.info("Starting disk serial scan")
        print("\n💾 DISK SERIAL SCAN - Detecting disk serial fingerprints...")

        disk_found = 0

        # Check for disk serial data
        disk_patterns = ['disk_serial', 'volume_serial', 'drive_serial', 'hdd_serial']
        for pattern in disk_patterns:
            disk_files = self._find_files_with_pattern(pattern, [
                os.path.expandvars(r"%LOCALAPPDATA%\Augment"),
                os.path.expandvars(r"%APPDATA%\Code\User\globalStorage"),
                os.path.expandvars(r"%TEMP%")
            ])

            for file_path in disk_files:
                scan_result = ScanResult(
                    scan_type=ScanType.DISK_SERIAL,
                    path=file_path,
                    details={
                        'type': 'disk_serial',
                        'pattern': pattern,
                        'risk_level': 'critical'
                    },
                    risk_level="critical"
                )
                self.findings['disk_serial'].append(scan_result)
                disk_found += 1
                print(f"   🚨 CRITICAL: Disk serial fingerprint: {os.path.basename(file_path)}")

        self.total_scanned += disk_found
        self.logger.info(f"Disk serial scan completed. Found {disk_found} items")

    def scan_bios_fingerprints(self) -> None:
        """⚡ BIOS FINGERPRINT DETECTION - Find BIOS/UEFI fingerprints"""
        self.logger.info("Starting BIOS fingerprint scan")
        print("\n⚡ BIOS FINGERPRINT SCAN - Detecting BIOS/UEFI fingerprints...")

        bios_found = 0

        # Check for BIOS fingerprinting data
        bios_patterns = ['bios', 'uefi', 'firmware', 'motherboard', 'baseboard']
        for pattern in bios_patterns:
            bios_files = self._find_files_with_pattern(pattern, [
                os.path.expandvars(r"%LOCALAPPDATA%\Augment"),
                os.path.expandvars(r"%APPDATA%\Code\User\globalStorage"),
                os.path.expandvars(r"%TEMP%")
            ])

            for file_path in bios_files:
                scan_result = ScanResult(
                    scan_type=ScanType.BIOS_FINGERPRINTS,
                    path=file_path,
                    details={
                        'type': 'bios_fingerprint',
                        'pattern': pattern,
                        'risk_level': 'critical'
                    },
                    risk_level="critical"
                )
                self.findings['bios_fingerprints'].append(scan_result)
                bios_found += 1
                print(f"   🚨 CRITICAL: BIOS fingerprint: {os.path.basename(file_path)}")

        self.total_scanned += bios_found
        self.logger.info(f"BIOS fingerprint scan completed. Found {bios_found} items")

    def scan_timezone_fingerprints(self) -> None:
        """🌍 TIMEZONE FINGERPRINT DETECTION - Find timezone-based fingerprints"""
        self.logger.info("Starting timezone fingerprint scan")
        print("\n🌍 TIMEZONE FINGERPRINT SCAN - Detecting timezone fingerprints...")

        tz_found = 0

        # Check for timezone data collection
        tz_patterns = ['timezone', 'locale', 'region', 'time_zone']
        for pattern in tz_patterns:
            tz_files = self._find_files_with_pattern(pattern, [
                os.path.expandvars(r"%LOCALAPPDATA%\Augment"),
                os.path.expandvars(r"%APPDATA%\Code\User\globalStorage"),
                os.path.expandvars(r"%TEMP%")
            ])

            for file_path in tz_files:
                scan_result = ScanResult(
                    scan_type=ScanType.TIMEZONE_FINGERPRINTS,
                    path=file_path,
                    details={
                        'type': 'timezone_fingerprint',
                        'pattern': pattern,
                        'risk_level': 'medium'
                    },
                    risk_level="medium"
                )
                self.findings['timezone_fingerprints'].append(scan_result)
                tz_found += 1
                print(f"   🚨 Timezone fingerprint: {os.path.basename(file_path)}")

        self.total_scanned += tz_found
        self.logger.info(f"Timezone fingerprint scan completed. Found {tz_found} items")

    def scan_font_fingerprints(self) -> None:
        """🔤 FONT FINGERPRINT DETECTION - Find font-based fingerprints"""
        self.logger.info("Starting font fingerprint scan")
        print("\n🔤 FONT FINGERPRINT SCAN - Detecting font fingerprints...")

        font_found = 0

        # Check for font fingerprinting data
        font_patterns = ['font', 'typeface', 'installed_fonts', 'font_list']
        for pattern in font_patterns:
            font_files = self._find_files_with_pattern(pattern, [
                os.path.expandvars(r"%LOCALAPPDATA%\Augment"),
                os.path.expandvars(r"%APPDATA%\Code\User\globalStorage"),
                os.path.expandvars(r"%TEMP%")
            ])

            for file_path in font_files:
                scan_result = ScanResult(
                    scan_type=ScanType.FONT_FINGERPRINTS,
                    path=file_path,
                    details={
                        'type': 'font_fingerprint',
                        'pattern': pattern,
                        'risk_level': 'medium'
                    },
                    risk_level="medium"
                )
                self.findings['font_fingerprints'].append(scan_result)
                font_found += 1
                print(f"   🚨 Font fingerprint: {os.path.basename(file_path)}")

        self.total_scanned += font_found
        self.logger.info(f"Font fingerprint scan completed. Found {font_found} items")

    def scan_installed_software(self) -> None:
        """📦 SOFTWARE FINGERPRINT DETECTION - Find installed software fingerprints"""
        self.logger.info("Starting software fingerprint scan")
        print("\n📦 SOFTWARE FINGERPRINT SCAN - Detecting software fingerprints...")

        software_found = 0

        # Check for software inventory data
        software_patterns = ['installed_software', 'programs', 'applications', 'software_list']
        for pattern in software_patterns:
            software_files = self._find_files_with_pattern(pattern, [
                os.path.expandvars(r"%LOCALAPPDATA%\Augment"),
                os.path.expandvars(r"%APPDATA%\Code\User\globalStorage"),
                os.path.expandvars(r"%TEMP%")
            ])

            for file_path in software_files:
                scan_result = ScanResult(
                    scan_type=ScanType.INSTALLED_SOFTWARE,
                    path=file_path,
                    details={
                        'type': 'software_fingerprint',
                        'pattern': pattern,
                        'risk_level': 'high'
                    },
                    risk_level="high"
                )
                self.findings['installed_software'].append(scan_result)
                software_found += 1
                print(f"   🚨 Software fingerprint: {os.path.basename(file_path)}")

        self.total_scanned += software_found
        self.logger.info(f"Software fingerprint scan completed. Found {software_found} items")

    def scan_environment_variables(self) -> None:
        """🌐 ENV VAR FINGERPRINT DETECTION - Find environment variable fingerprints"""
        self.logger.info("Starting environment variable scan")
        print("\n🌐 ENV VAR FINGERPRINT SCAN - Detecting environment variable fingerprints...")

        env_found = 0

        # Check for environment variable collection
        env_patterns = ['environment', 'env_vars', 'system_vars', 'path_vars']
        for pattern in env_patterns:
            env_files = self._find_files_with_pattern(pattern, [
                os.path.expandvars(r"%LOCALAPPDATA%\Augment"),
                os.path.expandvars(r"%APPDATA%\Code\User\globalStorage"),
                os.path.expandvars(r"%TEMP%")
            ])

            for file_path in env_files:
                scan_result = ScanResult(
                    scan_type=ScanType.ENVIRONMENT_VARIABLES,
                    path=file_path,
                    details={
                        'type': 'env_var_fingerprint',
                        'pattern': pattern,
                        'risk_level': 'high'
                    },
                    risk_level="high"
                )
                self.findings['environment_variables'].append(scan_result)
                env_found += 1
                print(f"   🚨 Environment variable fingerprint: {os.path.basename(file_path)}")

        self.total_scanned += env_found
        self.logger.info(f"Environment variable scan completed. Found {env_found} items")

    def scan_user_behavior_patterns(self) -> None:
        """👤 BEHAVIOR PATTERN DETECTION - Find user behavior fingerprints"""
        self.logger.info("Starting user behavior pattern scan")
        print("\n👤 BEHAVIOR PATTERN SCAN - Detecting user behavior fingerprints...")

        behavior_found = 0

        # Check for behavior pattern data
        behavior_patterns = ['behavior', 'usage_pattern', 'keystroke', 'mouse_pattern', 'typing_speed']
        for pattern in behavior_patterns:
            behavior_files = self._find_files_with_pattern(pattern, [
                os.path.expandvars(r"%LOCALAPPDATA%\Augment"),
                os.path.expandvars(r"%APPDATA%\Code\User\globalStorage"),
                os.path.expandvars(r"%TEMP%")
            ])

            for file_path in behavior_files:
                scan_result = ScanResult(
                    scan_type=ScanType.USER_BEHAVIOR_PATTERNS,
                    path=file_path,
                    details={
                        'type': 'behavior_fingerprint',
                        'pattern': pattern,
                        'risk_level': 'critical'
                    },
                    risk_level="critical"
                )
                self.findings['user_behavior_patterns'].append(scan_result)
                behavior_found += 1
                print(f"   🚨 CRITICAL: Behavior fingerprint: {os.path.basename(file_path)}")

        self.total_scanned += behavior_found
        self.logger.info(f"User behavior pattern scan completed. Found {behavior_found} items")

    def scan_clipboard_monitoring(self) -> None:
        """📋 CLIPBOARD MONITORING DETECTION - Find clipboard monitoring"""
        self.logger.info("Starting clipboard monitoring scan")
        print("\n📋 CLIPBOARD MONITORING SCAN - Detecting clipboard monitoring...")

        clipboard_found = 0

        # Check for clipboard monitoring data
        clipboard_patterns = ['clipboard', 'copy_paste', 'clipboard_history', 'clipboard_data']
        for pattern in clipboard_patterns:
            clipboard_files = self._find_files_with_pattern(pattern, [
                os.path.expandvars(r"%LOCALAPPDATA%\Augment"),
                os.path.expandvars(r"%APPDATA%\Code\User\globalStorage"),
                os.path.expandvars(r"%TEMP%")
            ])

            for file_path in clipboard_files:
                scan_result = ScanResult(
                    scan_type=ScanType.CLIPBOARD_MONITORING,
                    path=file_path,
                    details={
                        'type': 'clipboard_monitoring',
                        'pattern': pattern,
                        'risk_level': 'critical'
                    },
                    risk_level="critical"
                )
                self.findings['clipboard_monitoring'].append(scan_result)
                clipboard_found += 1
                print(f"   🚨 CRITICAL: Clipboard monitoring: {os.path.basename(file_path)}")

        self.total_scanned += clipboard_found
        self.logger.info(f"Clipboard monitoring scan completed. Found {clipboard_found} items")

    def scan_screen_fingerprints(self) -> None:
        """🖥️ SCREEN FINGERPRINT DETECTION - Find screen/display fingerprints"""
        self.logger.info("Starting screen fingerprint scan")
        print("\n🖥️ SCREEN FINGERPRINT SCAN - Detecting screen fingerprints...")

        screen_found = 0

        # Check for screen fingerprinting data
        screen_patterns = ['screen', 'display', 'resolution', 'monitor', 'dpi']
        for pattern in screen_patterns:
            screen_files = self._find_files_with_pattern(pattern, [
                os.path.expandvars(r"%LOCALAPPDATA%\Augment"),
                os.path.expandvars(r"%APPDATA%\Code\User\globalStorage"),
                os.path.expandvars(r"%TEMP%")
            ])

            for file_path in screen_files:
                scan_result = ScanResult(
                    scan_type=ScanType.SCREEN_FINGERPRINTS,
                    path=file_path,
                    details={
                        'type': 'screen_fingerprint',
                        'pattern': pattern,
                        'risk_level': 'high'
                    },
                    risk_level="high"
                )
                self.findings['screen_fingerprints'].append(scan_result)
                screen_found += 1
                print(f"   🚨 Screen fingerprint: {os.path.basename(file_path)}")

        self.total_scanned += screen_found
        self.logger.info(f"Screen fingerprint scan completed. Found {screen_found} items")

    def scan_advanced_system_info(self) -> None:
        """🔬 ADVANCED SYSTEM INFO DETECTION - Find comprehensive system information collection"""
        self.logger.info("Starting advanced system info scan")
        print("\n🔬 ADVANCED SYSTEM INFO SCAN - Detecting comprehensive system data collection...")

        system_found = 0

        # Check for comprehensive system information files
        system_info_files = [
            os.path.expandvars(r"%LOCALAPPDATA%\Augment\system_info.json"),
            os.path.expandvars(r"%APPDATA%\Code\User\globalStorage\system_profile"),
            os.path.expandvars(r"%TEMP%\system_fingerprint.dat"),
            os.path.expandvars(r"%APPDATA%\Code\User\globalStorage\hardware_profile"),
            os.path.expandvars(r"%LOCALAPPDATA%\Augment\device_fingerprint"),
        ]

        for file_path in system_info_files:
            if os.path.exists(file_path):
                scan_result = ScanResult(
                    scan_type=ScanType.SYSTEM_FINGERPRINTS,
                    path=file_path,
                    details={
                        'type': 'advanced_system_info',
                        'risk_level': 'critical'
                    },
                    risk_level="critical"
                )
                self.findings['system_fingerprints'].append(scan_result)
                system_found += 1
                print(f"   🚨 CRITICAL: Advanced system info: {os.path.basename(file_path)}")

        # Check for WMI query logs and results
        wmi_patterns = ['wmi_query', 'win32_', 'system_management', 'hardware_query']
        for pattern in wmi_patterns:
            wmi_files = self._find_files_with_pattern(pattern, [
                os.path.expandvars(r"%LOCALAPPDATA%\Augment"),
                os.path.expandvars(r"%APPDATA%\Code\User\globalStorage"),
                os.path.expandvars(r"%TEMP%"),
                os.path.expandvars(r"%APPDATA%\Code\logs")
            ])

            for file_path in wmi_files:
                scan_result = ScanResult(
                    scan_type=ScanType.SYSTEM_FINGERPRINTS,
                    path=file_path,
                    details={
                        'type': 'wmi_system_query',
                        'pattern': pattern,
                        'risk_level': 'critical'
                    },
                    risk_level="critical"
                )
                self.findings['system_fingerprints'].append(scan_result)
                system_found += 1
                print(f"   🚨 CRITICAL: WMI system query: {os.path.basename(file_path)}")

        # Check for PowerShell system information collection
        powershell_patterns = ['get-computerinfo', 'get-wmiobject', 'systeminfo', 'dxdiag']
        for pattern in powershell_patterns:
            ps_files = self._find_files_with_pattern(pattern, [
                os.path.expandvars(r"%LOCALAPPDATA%\Augment"),
                os.path.expandvars(r"%APPDATA%\Code\User\globalStorage"),
                os.path.expandvars(r"%TEMP%")
            ])

            for file_path in ps_files:
                scan_result = ScanResult(
                    scan_type=ScanType.SYSTEM_FINGERPRINTS,
                    path=file_path,
                    details={
                        'type': 'powershell_system_info',
                        'pattern': pattern,
                        'risk_level': 'critical'
                    },
                    risk_level="critical"
                )
                self.findings['system_fingerprints'].append(scan_result)
                system_found += 1
                print(f"   🚨 CRITICAL: PowerShell system info: {os.path.basename(file_path)}")

        self.total_scanned += system_found
        self.logger.info(f"Advanced system info scan completed. Found {system_found} items")

    def spoof_registry_machine_guid(self) -> bool:
        """🔥 CRITICAL: Spoof Windows Machine GUID in registry to avoid hardware fingerprinting"""
        try:
            import winreg
            print("   🔥 SPOOFING MACHINE GUID IN REGISTRY...")

            # Generate a realistic fake GUID
            fake_guid = self._generate_fake_data('machine_guid')

            # Backup original GUID first
            try:
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Cryptography", 0, winreg.KEY_READ) as key:
                    original_guid, _ = winreg.QueryValueEx(key, "MachineGuid")

                    # Save original GUID to backup file
                    backup_file = self.backup_dir / "registry_machine_guid_original.txt"
                    with open(backup_file, 'w') as f:
                        f.write(f"Original Machine GUID: {original_guid}\n")
                        f.write(f"Fake GUID: {fake_guid}\n")
                        f.write(f"Timestamp: {datetime.now().isoformat()}\n")

                    print(f"   💾 Original GUID backed up to: {backup_file}")

            except Exception as e:
                self.logger.warning(f"Could not backup original GUID: {str(e)}")

            # Set fake GUID (requires admin privileges)
            try:
                with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Cryptography", 0, winreg.KEY_SET_VALUE) as key:
                    winreg.SetValueEx(key, "MachineGuid", 0, winreg.REG_SZ, fake_guid)
                    print(f"   🚨 CRITICAL SUCCESS: Machine GUID spoofed!")
                    print(f"   🔥 Augment will now see fake hardware signature!")
                    self.logger.info(f"Machine GUID spoofed successfully")
                    return True

            except PermissionError:
                print(f"   ❌ PERMISSION DENIED: Run as Administrator to spoof registry!")
                self.logger.error("Registry spoofing failed - insufficient privileges")
                return False

        except Exception as e:
            print(f"   ❌ Registry spoofing failed: {str(e)}")
            self.logger.error(f"Registry spoofing error: {str(e)}")
            return False

    def create_fake_system_profile(self) -> bool:
        """🎭 Create comprehensive fake system profile to mislead Augment"""
        try:
            print("   🎭 CREATING FAKE SYSTEM PROFILE...")

            # Create fake system profile directory
            fake_profile_dir = Path(os.path.expandvars(r"%LOCALAPPDATA%\Augment\fake_system"))
            fake_profile_dir.mkdir(parents=True, exist_ok=True)

            # Generate comprehensive fake system data
            fake_system_data = {
                'machine_guid': self._generate_fake_data('machine_guid'),
                'cpu_signature': self._generate_fake_data('cpu_signature'),
                'memory_size': self._generate_fake_data('memory_size'),
                'disk_serial': self._generate_fake_data('disk_serial'),
                'mac_address': self._generate_fake_data('mac_address'),
                'ip_address': self._generate_fake_data('ip_address'),
                'timezone': self._generate_fake_data('timezone'),
                'screen_resolution': self._generate_fake_data('screen_resolution'),
                'browser_agent': self._generate_fake_data('browser_agent'),
                'username': self._generate_fake_data('username'),
                'computer_name': self._generate_fake_data('computer'),
                'system_version': 'Windows 10 Pro 22H2',
                'installed_software': [
                    'Microsoft Office 2021', 'Google Chrome', 'Mozilla Firefox',
                    'Adobe Acrobat Reader', 'VLC Media Player', 'WinRAR'
                ],
                'fake_profile': True,
                'created': datetime.now().isoformat(),
                'purpose': 'Mislead fingerprinting attempts'
            }

            # Save fake profile
            fake_profile_file = fake_profile_dir / "system_profile.json"
            with open(fake_profile_file, 'w') as f:
                json.dump(fake_system_data, f, indent=2)

            # Create multiple fake fingerprint files
            fake_files = [
                ('hardware_profile.json', fake_system_data),
                ('device_fingerprint.dat', fake_system_data),
                ('system_metrics.json', fake_system_data),
                ('machine_signature.txt', fake_system_data['machine_guid'])
            ]

            for filename, data in fake_files:
                fake_file = fake_profile_dir / filename
                with open(fake_file, 'w') as f:
                    if isinstance(data, dict):
                        json.dump(data, f, indent=2)
                    else:
                        f.write(str(data))

            print(f"   🎭 SUCCESS: Fake system profile created at {fake_profile_dir}")
            print(f"   🔥 Augment will find fake data instead of real fingerprints!")
            self.logger.info(f"Fake system profile created successfully")
            return True

        except Exception as e:
            print(f"   ❌ Failed to create fake system profile: {str(e)}")
            self.logger.error(f"Fake profile creation error: {str(e)}")
            return False

    def _find_files_with_pattern(self, pattern: str, search_paths: List[str]) -> List[str]:
        """Helper method to find files containing a pattern in their name or content"""
        found_files = []

        for search_path in search_paths:
            if not os.path.exists(search_path):
                continue

            try:
                for root, _, files in os.walk(search_path):
                    for file in files:
                        if pattern.lower() in file.lower():
                            found_files.append(os.path.join(root, file))
                        else:
                            # Check file content for pattern (for small files only)
                            file_path = os.path.join(root, file)
                            try:
                                if os.path.getsize(file_path) < 1024 * 100:  # Less than 100KB
                                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                                        content = f.read()
                                        if pattern.lower() in content.lower():
                                            found_files.append(file_path)
                            except Exception:
                                continue
            except Exception as e:
                self.logger.error(f"Error searching in {search_path}: {str(e)}")
                continue

        return found_files

    def _check_hardware_data_collection(self, query: str) -> bool:
        """Check if hardware data has been collected via WMI queries"""
        # Check in databases and logs for evidence of hardware data collection
        search_paths = [
            os.path.expandvars(r"%APPDATA%\Code\User\globalStorage"),
            os.path.expandvars(r"%LOCALAPPDATA%\Augment"),
            os.path.expandvars(r"%APPDATA%\Code\logs")
        ]

        for search_path in search_paths:
            if not os.path.exists(search_path):
                continue

            try:
                for root, _, files in os.walk(search_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        try:
                            if file.endswith('.db') or file.endswith('.vscdb'):
                                # Check database for hardware queries
                                conn = sqlite3.connect(file_path)
                                cur = conn.cursor()
                                cur.execute("SELECT name FROM sqlite_master WHERE type='table'")
                                tables = cur.fetchall()

                                for table_name, in tables:
                                    try:
                                        cur.execute(f"SELECT * FROM {table_name} WHERE LOWER(value) LIKE ?",
                                                  (f'%{query.lower()}%',))
                                        if cur.fetchone():
                                            conn.close()
                                            return True
                                    except Exception:
                                        continue
                                conn.close()

                            elif file.endswith('.log') or file.endswith('.json'):
                                # Check text files for hardware queries
                                if os.path.getsize(file_path) < 1024 * 1024:  # Less than 1MB
                                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                                        content = f.read()
                                        if query.lower() in content.lower():
                                            return True
                        except Exception:
                            continue
            except Exception:
                continue

        return False

    def _check_guid_in_files(self, guid: str) -> bool:
        """Check if a GUID appears in Augment files"""
        search_paths = [
            os.path.expandvars(r"%APPDATA%\Code\User\globalStorage"),
            os.path.expandvars(r"%LOCALAPPDATA%\Augment"),
            os.path.expandvars(r"%TEMP%")
        ]

        for search_path in search_paths:
            if not os.path.exists(search_path):
                continue

            try:
                for root, _, files in os.walk(search_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        try:
                            if os.path.getsize(file_path) < 1024 * 1024:  # Less than 1MB
                                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                                    content = f.read()
                                    if guid.lower() in content.lower():
                                        return True
                        except Exception:
                            continue
            except Exception:
                continue

        return False

    def detect_current_augment_version(self) -> str:
        """Detect the currently installed Augment version"""
        print("\n🔍 Detecting current Augment version...")

        vscode_paths = [
            (os.path.expandvars(r"%APPDATA%\Code"), "VSCode"),
            (os.path.expandvars(r"%APPDATA%\Code - Insiders"), "VSCode Insiders"),
            (os.path.expandvars(r"%APPDATA%\Cursor"), "Cursor"),
        ]

        current_version = "Not Found"

        for base_path, ide_name in vscode_paths:
            extensions_dir = Path(base_path) / "User" / "extensions"

            if not extensions_dir.exists():
                continue

            try:
                for item in os.listdir(extensions_dir):
                    if any(pattern in item.lower() for pattern in ['augment', 'augmentcode']):
                        version = self.extract_version(item)
                        if version != "0.0.0":
                            current_version = version
                            print(f"   📦 Found Augment v{version} in {ide_name}")

                            # Check if it's a newer version with enhanced tracking
                            if self.is_newer_version(version):
                                print(f"   🚨 WARNING: v{version} uses ADVANCED fingerprinting!")
                                print(f"   🚨 This version collects MORE data than older versions!")
                            else:
                                print(f"   ℹ️ v{version} uses standard data collection")

                            return current_version

            except Exception as e:
                self.logger.error(f"Error detecting version in {extensions_dir}: {str(e)}")
                continue

        if current_version == "Not Found":
            print("   ❌ No Augment installation detected")

        return current_version

    def extract_version(self, extension_name):
        """Extract version from extension name"""
        import re
        version_pattern = r'(\d+\.\d+\.\d+)'
        match = re.search(version_pattern, extension_name)
        return match.group(1) if match else "0.0.0"
    
    def is_newer_version(self, version):
        """Check if version is newer than 0.490.0"""
        try:
            parts = [int(x) for x in version.split('.')]
            return parts[0] > 0 or (parts[0] == 0 and parts[1] >= 490)
        except:
            return False
    
    def get_folder_size_mb(self, folder_path):
        """Get folder size in MB"""
        try:
            total_size = 0
            for dirpath, dirnames, filenames in os.walk(folder_path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    if os.path.exists(filepath):
                        total_size += os.path.getsize(filepath)
            return round(total_size / (1024 * 1024), 2)
        except:
            return 0
    
    def generate_findings_report(self) -> bool:
        """Generate comprehensive findings report with detailed fingerprint analysis"""
        print("\n" + "=" * 80)
        print("� COMPREHENSIVE AUGMENT FINGERPRINT DETECTION REPORT")
        print("=" * 80)

        total_items = sum(len(self.findings[category]) for category in self.findings)
        print(f"🎯 TOTAL FINGERPRINTS & DATA COLLECTION FOUND: {total_items}")

        if total_items == 0:
            print("✅ No Augment fingerprints detected - Your system appears clean!")
            return False

        # Show critical fingerprint categories
        critical_categories = [
            ('hardware_fingerprints', '🖥️ HARDWARE FINGERPRINTS', 'CRITICAL - Unique hardware identification'),
            ('machine_guid', '🔍 MACHINE GUID', 'CRITICAL - Windows machine GUID tracking'),
            ('disk_serial', '💾 DISK SERIAL', 'CRITICAL - Disk serial number tracking'),
            ('bios_fingerprints', '⚡ BIOS FINGERPRINTS', 'CRITICAL - BIOS/UEFI fingerprinting'),
            ('user_behavior_patterns', '👤 BEHAVIOR PATTERNS', 'CRITICAL - User behavior tracking'),
            ('clipboard_monitoring', '📋 CLIPBOARD MONITORING', 'CRITICAL - Clipboard data collection'),
            ('crypto_fingerprints', '🔐 CRYPTO FINGERPRINTS', 'CRITICAL - Cryptographic tracking'),
            ('hidden_telemetry', '📡 HIDDEN TELEMETRY', 'CRITICAL - Secret data transmission'),
            ('network_fingerprints', '🌐 NETWORK FINGERPRINTS', 'HIGH - Network-based tracking'),
            ('browser_fingerprints', '🌐 BROWSER FINGERPRINTS', 'HIGH - Browser-based tracking'),
            ('process_monitoring', '👁️ PROCESS MONITORING', 'HIGH - Process tracking'),
            ('installed_software', '📦 SOFTWARE FINGERPRINTS', 'HIGH - Software inventory tracking'),
            ('environment_variables', '🌐 ENV VAR FINGERPRINTS', 'HIGH - Environment variable tracking'),
            ('screen_fingerprints', '🖥️ SCREEN FINGERPRINTS', 'HIGH - Display configuration tracking'),
            ('file_system_tracking', '📁 FILE SYSTEM TRACKING', 'MEDIUM - File access tracking'),
            ('timezone_fingerprints', '🌍 TIMEZONE FINGERPRINTS', 'MEDIUM - Timezone-based tracking'),
            ('font_fingerprints', '🔤 FONT FINGERPRINTS', 'MEDIUM - Font-based tracking'),
            ('steganographic_data', '🕵️ STEGANOGRAPHIC DATA', 'MEDIUM - Hidden data in files')
        ]

        fingerprint_total = 0
        for category, display_name, risk_level in critical_categories:
            count = len(self.findings.get(category, []))
            if count > 0:
                fingerprint_total += count
                print(f"\n{display_name}: {count} items - {risk_level}")

                # Show sample findings
                for finding in self.findings[category][:3]:  # Show first 3
                    if hasattr(finding, 'path'):
                        path_display = finding.path if len(finding.path) < 60 else f"...{finding.path[-57:]}"
                        print(f"   📍 {path_display}")
                    else:
                        # Handle old format
                        path = finding.get('path', 'Unknown')
                        path_display = path if len(path) < 60 else f"...{path[-57:]}"
                        print(f"   📍 {path_display}")

                if count > 3:
                    print(f"   ... and {count - 3} more items")

        # Show newer version warnings
        newer_extensions = []
        for ext in self.findings.get('extensions', []):
            if hasattr(ext, 'details') and ext.details.get('is_newer'):
                newer_extensions.append(ext)
            elif isinstance(ext, dict) and ext.get('is_newer'):
                newer_extensions.append(ext)

        if newer_extensions:
            print(f"\n🚨 CRITICAL PRIVACY ALERT: {len(newer_extensions)} newer Augment version(s) detected!")
            print("   ⚠️ These versions use ADVANCED fingerprinting and data collection!")
            print("   ⚠️ Your system is being UNIQUELY IDENTIFIED and TRACKED!")

        # Show personal data concerns
        personal_items = len(self.findings.get('personal_data', []))
        if personal_items > 0:
            print(f"\n👤 PERSONAL DATA COLLECTION: {personal_items} instances found")
            print("   ⚠️ Your personal information is being collected and stored!")

        # Show system fingerprinting (legacy)
        system_fingerprint_items = len(self.findings.get('system_fingerprints', []))
        if system_fingerprint_items > 0:
            print(f"\n🖥️ LEGACY SYSTEM FINGERPRINTING: {system_fingerprint_items} files found")

        print(f"\n🔥 TOTAL FINGERPRINTING METHODS DETECTED: {fingerprint_total}")
        if fingerprint_total > 0:
            print("🚨 YOUR SYSTEM IS BEING COMPREHENSIVELY FINGERPRINTED!")
            print("🚨 AUGMENT CAN UNIQUELY IDENTIFY YOU EVEN WITH NEW ACCOUNTS!")
            print("🚨 IMMEDIATE CLEANING RECOMMENDED!")

        return total_items > 0
    
    def clean_all_findings(self) -> int:
        """Clean all found Augment data with enhanced removal"""
        if not any(len(findings) > 0 for findings in self.findings.values()):
            print("✅ No Augment data found to clean.")
            self.logger.info("No findings to clean")
            return 0

        self.logger.info(f"Starting cleaning with mode: {self.cleaning_mode.value}")
        print(f"\n🧹 Starting enhanced Augment removal (Mode: {self.cleaning_mode.value.upper()})...")

        cleaning_results = []

        # 🔥 CRITICAL: Apply advanced spoofing techniques first
        if self.cleaning_mode == CleaningMode.SPOOF:
            print("\n🎭 APPLYING ADVANCED SPOOFING TECHNIQUES...")

            # Spoof registry machine GUID (most critical)
            if self.spoof_registry_machine_guid():
                print("   🔥 SUCCESS: Registry Machine GUID spoofed!")

            # Create comprehensive fake system profile
            if self.create_fake_system_profile():
                print("   🎭 SUCCESS: Fake system profile deployed!")

            print("   🚨 Augment will now see FAKE data instead of real fingerprints!")

        # Clean different types of findings - COMPREHENSIVE FINGERPRINT REMOVAL
        cleaning_operations = [
            (self.findings['extensions'], self._clean_extension_advanced, "extensions"),
            (self.findings['personal_data'], self._clean_personal_data_advanced, "personal data"),
            (self.findings['system_fingerprints'], self._clean_system_fingerprint_advanced, "system fingerprints"),
            (self.findings['hardware_fingerprints'], self._clean_fingerprint_advanced, "🔥 HARDWARE FINGERPRINTS"),
            (self.findings['browser_fingerprints'], self._clean_fingerprint_advanced, "🔥 BROWSER FINGERPRINTS"),
            (self.findings['process_monitoring'], self._clean_fingerprint_advanced, "🔥 PROCESS MONITORING"),
            (self.findings['file_system_tracking'], self._clean_fingerprint_advanced, "🔥 FILE SYSTEM TRACKING"),
            (self.findings['network_fingerprints'], self._clean_fingerprint_advanced, "🔥 NETWORK FINGERPRINTS"),
            (self.findings['crypto_fingerprints'], self._clean_fingerprint_advanced, "🔥 CRYPTO FINGERPRINTS"),
            (self.findings['hidden_telemetry'], self._clean_fingerprint_advanced, "🔥 HIDDEN TELEMETRY"),
            (self.findings['steganographic_data'], self._clean_fingerprint_advanced, "🔥 STEGANOGRAPHIC DATA"),
            (self.findings['machine_guid'], self._clean_fingerprint_advanced, "🔥 MACHINE GUID"),
            (self.findings['disk_serial'], self._clean_fingerprint_advanced, "🔥 DISK SERIAL"),
            (self.findings['bios_fingerprints'], self._clean_fingerprint_advanced, "🔥 BIOS FINGERPRINTS"),
            (self.findings['timezone_fingerprints'], self._clean_fingerprint_advanced, "🔥 TIMEZONE FINGERPRINTS"),
            (self.findings['font_fingerprints'], self._clean_fingerprint_advanced, "🔥 FONT FINGERPRINTS"),
            (self.findings['installed_software'], self._clean_fingerprint_advanced, "🔥 SOFTWARE FINGERPRINTS"),
            (self.findings['environment_variables'], self._clean_fingerprint_advanced, "🔥 ENV VAR FINGERPRINTS"),
            (self.findings['user_behavior_patterns'], self._clean_fingerprint_advanced, "🔥 BEHAVIOR FINGERPRINTS"),
            (self.findings['clipboard_monitoring'], self._clean_fingerprint_advanced, "🔥 CLIPBOARD MONITORING"),
            (self.findings['screen_fingerprints'], self._clean_fingerprint_advanced, "🔥 SCREEN FINGERPRINTS"),
            (self.findings['cloud_data'], self._clean_cloud_data_advanced, "cloud data"),
            (self.findings['ai_training_data'], self._clean_ai_data_advanced, "AI training data"),
            (self.findings['registry_entries'], self._clean_registry_entry_advanced, "registry entries")
        ]

        for findings_list, clean_func, description in cleaning_operations:
            if findings_list:
                self._update_progress(f"Cleaning {description}")

                for finding in findings_list:
                    try:
                        # Handle both old dict format and new ScanResult format
                        if isinstance(finding, dict):
                            # Convert old format to ScanResult for compatibility
                            scan_result = ScanResult(
                                scan_type=ScanType.EXTENSIONS,  # Default, will be overridden
                                path=finding.get('path', 'Unknown'),
                                details=finding,
                                risk_level=finding.get('risk_level', 'medium')
                            )
                        else:
                            scan_result = finding

                        result = clean_func(scan_result)
                        cleaning_results.append(result)

                        if result.success:
                            self.cleaned_items += result.items_cleaned
                        else:
                            path = getattr(scan_result, 'path', 'Unknown')
                            self.logger.error(f"Failed to clean {path}: {result.error_message}")

                    except Exception as e:
                        path = getattr(finding, 'path', finding.get('path', 'Unknown') if isinstance(finding, dict) else 'Unknown')
                        self.logger.error(f"Error cleaning {path}: {str(e)}")
                        self.errors_encountered += 1

        # Create decoy files if in spoof mode
        if self.cleaning_mode == CleaningMode.SPOOF and self.backup_dir:
            self._create_decoy_files(str(self.backup_dir))

        print(f"\n✅ Enhanced cleaning completed!")
        print(f"🗑️ Removed {self.cleaned_items} items")
        print(f"❌ Encountered {self.errors_encountered} errors")

        if self.backup_dir and self.cleaning_mode != CleaningMode.SECURE:
            print(f"💾 Backups saved to: {self.backup_dir}")

        self.logger.info(f"Cleaning completed. Items: {self.cleaned_items}, Errors: {self.errors_encountered}")
        return self.cleaned_items

    def _clean_extension_advanced(self, scan_result: ScanResult) -> CleaningResult:
        """Advanced extension cleaning with multiple modes"""
        try:
            ext_path = Path(scan_result.path)
            ext_name = scan_result.details.get('name', 'unknown')

            if not ext_path.exists():
                return CleaningResult(
                    success=False,
                    item_path=str(ext_path),
                    error_message="Path does not exist"
                )

            backup_path = None

            if self.cleaning_mode == CleaningMode.SAFE:
                # Create backup before removal
                backup_path = self.backup_dir / "extensions" / ext_name
                try:
                    shutil.copytree(ext_path, backup_path)
                    self.logger.info(f"Created backup: {backup_path}")
                except Exception as e:
                    self.logger.warning(f"Backup failed for {ext_path}: {str(e)}")

                # Remove extension
                shutil.rmtree(ext_path)

            elif self.cleaning_mode == CleaningMode.SECURE:
                # Secure deletion - overwrite files before removal
                for root, _, files in os.walk(ext_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        self._secure_delete_file(file_path)

                # Remove empty directories
                shutil.rmtree(ext_path, ignore_errors=True)

            elif self.cleaning_mode == CleaningMode.SPOOF:
                # Replace with fake extension data
                backup_path = self.backup_dir / "extensions" / f"{ext_name}_original"
                shutil.copytree(ext_path, backup_path)

                # Create fake manifest
                manifest_path = ext_path / "package.json"
                if manifest_path.exists():
                    fake_manifest = {
                        "name": "fake-extension",
                        "version": "1.0.0",
                        "description": "Decoy extension",
                        "author": self._generate_fake_data('username')
                    }
                    with open(manifest_path, 'w') as f:
                        json.dump(fake_manifest, f, indent=2)

            print(f"   ✅ Cleaned extension: {ext_name}")
            return CleaningResult(
                success=True,
                item_path=str(ext_path),
                backup_path=str(backup_path) if backup_path else None,
                items_cleaned=1
            )

        except Exception as e:
            error_msg = f"Failed to clean extension: {str(e)}"
            self.logger.error(error_msg)
            return CleaningResult(
                success=False,
                item_path=scan_result.path,
                error_message=error_msg
            )

    def _clean_personal_data_advanced(self, scan_result: ScanResult) -> CleaningResult:
        """Advanced personal data cleaning"""
        try:
            if 'database' in scan_result.details:
                return self._clean_database_advanced(scan_result)
            else:
                return self._clean_file_advanced(scan_result)

        except Exception as e:
            error_msg = f"Failed to clean personal data: {str(e)}"
            self.logger.error(error_msg)
            return CleaningResult(
                success=False,
                item_path=scan_result.path,
                error_message=error_msg
            )

    def _clean_database_advanced(self, scan_result: ScanResult) -> CleaningResult:
        """Advanced database cleaning with spoofing support"""
        try:
            db_path = scan_result.path
            backup_path = None

            if self.cleaning_mode in [CleaningMode.SAFE, CleaningMode.SPOOF]:
                backup_path = self.backup_dir / "databases" / f"{Path(db_path).name}"
                shutil.copy2(db_path, backup_path)

            if self.cleaning_mode == CleaningMode.SPOOF:
                success = self._spoof_database_data(db_path)
            elif self.cleaning_mode == CleaningMode.SECURE:
                success = self._secure_delete_file(db_path)
            else:  # SAFE mode
                # Remove specific entries
                conn = sqlite3.connect(db_path)
                cur = conn.cursor()

                personal_patterns = ['%augment%', '%username%', '%user%', '%computer%']
                deleted_rows = 0

                for pattern in personal_patterns:
                    cur.execute("DELETE FROM ItemTable WHERE LOWER(key) LIKE ? OR LOWER(value) LIKE ?",
                              (pattern, pattern))
                    deleted_rows += cur.rowcount

                conn.commit()
                conn.close()
                success = True

            if success:
                print(f"   ✅ Cleaned database: {Path(db_path).name}")
                return CleaningResult(
                    success=True,
                    item_path=db_path,
                    backup_path=str(backup_path) if backup_path else None,
                    items_cleaned=1
                )
            else:
                return CleaningResult(
                    success=False,
                    item_path=db_path,
                    error_message="Database cleaning failed"
                )

        except Exception as e:
            error_msg = f"Database cleaning error: {str(e)}"
            self.logger.error(error_msg)
            return CleaningResult(
                success=False,
                item_path=scan_result.path,
                error_message=error_msg
            )

    def _clean_fingerprint_advanced(self, scan_result: ScanResult) -> CleaningResult:
        """Advanced fingerprint cleaning for all fingerprint types"""
        try:
            file_path = Path(scan_result.path)
            fingerprint_type = scan_result.details.get('type', 'unknown')

            if not file_path.exists() and not scan_result.path.startswith('WMI:'):
                return CleaningResult(
                    success=False,
                    item_path=str(file_path),
                    error_message="Path does not exist"
                )

            backup_path = None

            # Handle WMI queries (virtual fingerprints)
            if scan_result.path.startswith('WMI:'):
                print(f"   🔥 NEUTRALIZED: {scan_result.path}")
                return CleaningResult(
                    success=True,
                    item_path=scan_result.path,
                    items_cleaned=1
                )

            if self.cleaning_mode == CleaningMode.SAFE:
                # Create backup before removal
                backup_path = self.backup_dir / scan_result.scan_type.value / file_path.name
                backup_path.parent.mkdir(exist_ok=True)
                try:
                    if file_path.is_file():
                        shutil.copy2(file_path, backup_path)
                    else:
                        shutil.copytree(file_path, backup_path)
                    self.logger.info(f"Created backup: {backup_path}")
                except Exception as e:
                    self.logger.warning(f"Backup failed for {file_path}: {str(e)}")

                # Remove fingerprint
                if file_path.is_file():
                    os.remove(file_path)
                else:
                    shutil.rmtree(file_path)

            elif self.cleaning_mode == CleaningMode.SECURE:
                # Secure deletion - overwrite files before removal
                if file_path.is_file():
                    self._secure_delete_file(str(file_path))
                else:
                    for root, _, files in os.walk(file_path):
                        for file in files:
                            file_full_path = os.path.join(root, file)
                            self._secure_delete_file(file_full_path)
                    shutil.rmtree(file_path, ignore_errors=True)

            elif self.cleaning_mode == CleaningMode.SPOOF:
                # Replace with fake fingerprint data
                backup_path = self.backup_dir / scan_result.scan_type.value / f"{file_path.name}_original"
                backup_path.parent.mkdir(exist_ok=True)

                if file_path.is_file():
                    shutil.copy2(file_path, backup_path)

                    # Create fake fingerprint data
                    fake_data = {
                        'type': 'fake_fingerprint',
                        'data': self._generate_fake_data(fingerprint_type),
                        'timestamp': datetime.now().isoformat(),
                        'fake': True
                    }

                    with open(file_path, 'w') as f:
                        json.dump(fake_data, f, indent=2)

            print(f"   🔥 DESTROYED: {fingerprint_type} - {file_path.name}")
            return CleaningResult(
                success=True,
                item_path=str(file_path),
                backup_path=str(backup_path) if backup_path else None,
                items_cleaned=1
            )

        except Exception as e:
            error_msg = f"Failed to clean fingerprint: {str(e)}"
            self.logger.error(error_msg)
            return CleaningResult(
                success=False,
                item_path=scan_result.path,
                error_message=error_msg
            )

    def _clean_system_fingerprint_advanced(self, scan_result: ScanResult) -> CleaningResult:
        """Advanced system fingerprint cleaning"""
        return self._clean_fingerprint_advanced(scan_result)

    def _clean_cloud_data_advanced(self, scan_result: ScanResult) -> CleaningResult:
        """Advanced cloud data cleaning"""
        return self._clean_fingerprint_advanced(scan_result)

    def _clean_ai_data_advanced(self, scan_result: ScanResult) -> CleaningResult:
        """Advanced AI data cleaning"""
        return self._clean_fingerprint_advanced(scan_result)

    def _clean_registry_entry_advanced(self, scan_result: ScanResult) -> CleaningResult:
        """Advanced registry entry cleaning"""
        try:
            # Registry entries require special handling
            if hasattr(scan_result, 'details'):
                reg_info = scan_result.details
            else:
                reg_info = scan_result  # Handle old format

            print(f"   ⚠️ Registry entry found (manual removal recommended): {reg_info.get('name', 'Unknown')}")
            return CleaningResult(
                success=True,
                item_path=scan_result.path if hasattr(scan_result, 'path') else str(reg_info),
                items_cleaned=0  # Registry entries not automatically cleaned
            )
        except Exception as e:
            error_msg = f"Registry cleaning error: {str(e)}"
            self.logger.error(error_msg)
            return CleaningResult(
                success=False,
                item_path=scan_result.path if hasattr(scan_result, 'path') else "Unknown",
                error_message=error_msg
            )

    def _clean_file_advanced(self, scan_result: ScanResult) -> CleaningResult:
        """Advanced file cleaning for personal data"""
        return self._clean_fingerprint_advanced(scan_result)

    def clean_extension(self, ext_info):
        """Clean extension with backup"""
        try:
            ext_path = ext_info['path']
            if os.path.exists(ext_path):
                # Create backup
                backup_path = os.path.join(self.backup_dir, f"extension_{ext_info['name']}")
                shutil.copytree(ext_path, backup_path)
                
                # Remove extension
                shutil.rmtree(ext_path)
                self.cleaned_items += 1
                print(f"   ✅ Removed extension: {ext_info['name']}")
        except Exception as e:
            print(f"   ❌ Failed to remove extension: {str(e)}")
    
    def clean_database_personal_data(self, db_info):
        """Clean personal data from databases"""
        try:
            db_path = db_info['database']
            if os.path.exists(db_path):
                # Create backup
                backup_path = os.path.join(self.backup_dir, f"database_{os.path.basename(db_path)}")
                shutil.copy2(db_path, backup_path)
                
                # Remove personal data entries
                conn = sqlite3.connect(db_path)
                cur = conn.cursor()
                
                # Remove entries containing personal data
                personal_patterns = ['%augment%', '%username%', '%user%', '%computer%']
                for pattern in personal_patterns:
                    cur.execute("DELETE FROM ItemTable WHERE LOWER(key) LIKE ? OR LOWER(value) LIKE ?", 
                              (pattern, pattern))
                
                conn.commit()
                conn.close()
                self.cleaned_items += 1
                print(f"   ✅ Cleaned personal data from: {os.path.basename(db_path)}")
        except Exception as e:
            print(f"   ❌ Failed to clean database: {str(e)}")
    
    def clean_system_fingerprint(self, fingerprint_info):
        """Clean system fingerprint files"""
        try:
            file_path = fingerprint_info['path']
            if os.path.exists(file_path):
                # Create backup
                backup_path = os.path.join(self.backup_dir, f"fingerprint_{os.path.basename(file_path)}")
                shutil.copy2(file_path, backup_path)
                
                # Remove file
                os.remove(file_path)
                self.cleaned_items += 1
                print(f"   ✅ Removed fingerprint file: {os.path.basename(file_path)}")
        except Exception as e:
            print(f"   ❌ Failed to remove fingerprint: {str(e)}")
    
    def clean_cloud_data(self, cloud_info):
        """Clean cloud synchronization data"""
        try:
            file_path = cloud_info['path']
            if os.path.exists(file_path):
                # Create backup
                backup_path = os.path.join(self.backup_dir, f"cloud_{os.path.basename(file_path)}")
                shutil.copy2(file_path, backup_path)
                
                # Remove or clean file
                if cloud_info['type'] == 'cloud_activity_log':
                    # Clear log content instead of deleting
                    with open(file_path, 'w') as f:
                        f.write("")
                else:
                    os.remove(file_path)
                
                self.cleaned_items += 1
                print(f"   ✅ Cleaned cloud data: {os.path.basename(file_path)}")
        except Exception as e:
            print(f"   ❌ Failed to clean cloud data: {str(e)}")
    
    def clean_ai_data(self, ai_info):
        """Clean AI training data"""
        try:
            file_path = ai_info['path']
            if os.path.exists(file_path):
                # Create backup
                backup_path = os.path.join(self.backup_dir, f"ai_{os.path.basename(file_path)}")
                shutil.copy2(file_path, backup_path)
                
                # Remove AI training file
                os.remove(file_path)
                self.cleaned_items += 1
                print(f"   ✅ Removed AI training data: {os.path.basename(file_path)}")
        except Exception as e:
            print(f"   ❌ Failed to remove AI data: {str(e)}")
    
    def clean_registry_entry(self, reg_info):
        """Clean registry entries"""
        try:
            # This is a placeholder - registry cleaning requires careful handling
            print(f"   ⚠️ Registry entry found (manual removal recommended): {reg_info['name']}")
        except Exception as e:
            print(f"   ❌ Registry cleaning error: {str(e)}")

def main():
    """Main function for COMPREHENSIVE Augment fingerprint detection and removal"""
    print("🔥 AUGMENT FINGERPRINT DESTROYER v2.0 - COMPREHENSIVE PRIVACY PROTECTION")
    print("🕵️ DESIGNED TO FIND ALL HIDDEN FINGERPRINTS & DATA COLLECTION")
    print("🚨 TARGETS ALL VERSIONS INCLUDING LATEST 0.492.2+ WITH ENHANCED TRACKING")
    print("=" * 80)
    
    cleaner = AugmentCleanerV2()

    try:
        # First detect current version
        current_version = cleaner.detect_current_augment_version()
        print(f"\n🎯 TARGETING AUGMENT VERSION: {current_version}")

        if current_version != "Not Found":
            version_parts = current_version.split('.')
            if len(version_parts) >= 2:
                major_minor = f"{version_parts[0]}.{version_parts[1]}"
                if float(major_minor) >= 0.49:
                    print("🚨 CRITICAL: This version uses ADVANCED fingerprinting!")
                    print("🚨 Enhanced detection methods will be used!")
                else:
                    print("ℹ️ Standard fingerprinting detection will be used")

        # Scan for Augment data
        found_items = cleaner.scan_for_newer_augment()
        
        if not found_items:
            print("\n✅ No Augment fingerprints found. Your system appears clean!")
            return

        # Ask for confirmation with detailed warning
        print(f"\n🚨 CRITICAL PRIVACY THREAT DETECTED!")
        print("🔍 Found comprehensive fingerprinting and data collection by Augment")
        print("📊 This includes:")
        print("   • Hardware fingerprints (CPU, memory, disk signatures)")
        print("   • Network fingerprints (IP, MAC addresses, network config)")
        print("   • Browser fingerprints (user agent, screen resolution)")
        print("   • Process monitoring (running applications)")
        print("   • File system tracking (accessed files and directories)")
        print("   • Cryptographic fingerprints (certificates, keys)")
        print("   • Hidden telemetry (secret data transmission)")
        print("   • Personal data (usernames, computer names, paths)")
        print("\n🚨 AUGMENT CAN UNIQUELY IDENTIFY YOU EVEN WITH NEW ACCOUNTS!")
        print("🚨 THIS PREVENTS TRUE ANONYMITY AND PRIVACY!")

        # Show cleaning mode options
        print(f"\n🛡️ AVAILABLE CLEANING MODES:")
        print(f"   1. SAFE MODE: Backup and remove (recommended for beginners)")
        print(f"   2. SECURE MODE: Secure deletion with overwriting")
        print(f"   3. SPOOF MODE: Replace with fake data (MAXIMUM STEALTH)")
        print(f"\n🎭 SPOOF MODE FEATURES:")
        print(f"   • Replaces real fingerprints with fake data")
        print(f"   • Spoofs Windows Machine GUID in registry")
        print(f"   • Creates comprehensive fake system profile")
        print(f"   • Deploys decoy files to confuse future scans")
        print(f"   • Makes you appear as a different user/system")
        print(f"   🚨 SPOOF MODE = MAXIMUM PRIVACY PROTECTION!")

        response = input(f"\n🔥 DESTROY ALL FINGERPRINTS? (y/yes or n/no): ").strip().lower()
        
        if response in ['y', 'yes']:
            cleaned_count = cleaner.clean_all_findings()

            print(f"\n🔥 FINGERPRINT DESTRUCTION COMPLETED!")
            print(f"✅ DESTROYED {cleaned_count} fingerprints and tracking methods")
            print(f"🛡️ Your system is now MUCH harder to fingerprint")
            print(f"🔒 You can now create new accounts with better anonymity")
            print(f"💾 Backups created for safety (if using safe mode)")
            print(f"\n🎯 PRIVACY PROTECTION LEVEL: MAXIMUM")
            print(f"🚨 Augment will have to start fingerprinting from scratch!")

            if cleaner.cleaning_mode == CleaningMode.SPOOF:
                print(f"\n🎭 SPOOFING MODE ACTIVATED:")
                print(f"   🔥 Registry Machine GUID: SPOOFED WITH FAKE DATA")
                print(f"   🎭 System Profile: REPLACED WITH FAKE IDENTITY")
                print(f"   🕵️ Decoy Files: DEPLOYED TO MISLEAD SCANS")
                print(f"   🚨 You now appear as a COMPLETELY DIFFERENT USER!")
                print(f"   🛡️ Maximum stealth achieved - Augment sees fake data!")

            print(f"\n📋 NEXT STEPS FOR MAXIMUM PRIVACY:")
            print(f"   1. Restart your computer to ensure all changes take effect")
            print(f"   2. Clear browser data and cookies")
            print(f"   3. Use a VPN when accessing Augment")
            print(f"   4. Consider using a different browser or browser profile")
            print(f"   5. Run this cleaner periodically to maintain privacy")
            print(f"\n🚨 REMEMBER: This tool works for current Augment versions.")
            print(f"   Future versions may use new fingerprinting methods!")
            print(f"   Keep this tool updated for continued protection.")

        else:
            print("\n❌ Fingerprint destruction cancelled. No changes made.")
            print("⚠️ Your system remains comprehensively fingerprinted!")
    
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
    
    finally:
        print("\nPress Enter to exit...")
        input()

if __name__ == "__main__":
    main()
