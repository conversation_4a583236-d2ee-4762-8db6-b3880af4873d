{"nodes": [{"id": 1, "callFrame": {"functionName": "(root)", "scriptId": "0", "url": "", "lineNumber": -1, "columnNumber": -1}, "hitCount": 0, "children": [2, 117, 119, 177]}, {"id": 2, "callFrame": {"functionName": "MessagePortMain._internalPort.emit", "scriptId": "117", "url": "node:electron/js2c/utility_init", "lineNumber": 1, "columnNumber": 2986}, "hitCount": 0, "children": [3]}, {"id": 3, "callFrame": {"functionName": "emit", "scriptId": "22", "url": "node:events", "lineNumber": 464, "columnNumber": 43}, "hitCount": 0, "children": [4]}, {"id": 4, "callFrame": {"functionName": "", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 360, "columnNumber": 1729}, "hitCount": 0, "children": [5]}, {"id": 5, "callFrame": {"functionName": "fire", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 28, "columnNumber": 9408}, "hitCount": 0, "children": [6]}, {"id": 6, "callFrame": {"functionName": "fire", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2476}, "hitCount": 0, "children": [7]}, {"id": 7, "callFrame": {"functionName": "B", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2286}, "hitCount": 0, "children": [8]}, {"id": 8, "callFrame": {"functionName": "", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 360, "columnNumber": 3347}, "hitCount": 0, "children": [9]}, {"id": 9, "callFrame": {"functionName": "fire", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 28, "columnNumber": 9408}, "hitCount": 0, "children": [10]}, {"id": 10, "callFrame": {"functionName": "fire", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2476}, "hitCount": 0, "children": [11]}, {"id": 11, "callFrame": {"functionName": "B", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2286}, "hitCount": 0, "children": [12]}, {"id": 12, "callFrame": {"functionName": "", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 28, "columnNumber": 113548}, "hitCount": 0, "children": [13]}, {"id": 13, "callFrame": {"functionName": "L", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 28, "columnNumber": 114552}, "hitCount": 0, "children": [14]}, {"id": 14, "callFrame": {"functionName": "M", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 28, "columnNumber": 115471}, "hitCount": 0, "children": [15, 176]}, {"id": 15, "callFrame": {"functionName": "S", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 28, "columnNumber": 116611}, "hitCount": 0, "children": [16]}, {"id": 16, "callFrame": {"functionName": "$onFileEvent", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 146, "columnNumber": 120745}, "hitCount": 0, "children": [17]}, {"id": 17, "callFrame": {"functionName": "fire", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2476}, "hitCount": 0, "children": [18]}, {"id": 18, "callFrame": {"functionName": "C", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2397}, "hitCount": 0, "children": [19]}, {"id": 19, "callFrame": {"functionName": "B", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2286}, "hitCount": 0, "children": [20]}, {"id": 20, "callFrame": {"functionName": "", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 146, "columnNumber": 118373}, "hitCount": 0, "children": [21, 175]}, {"id": 21, "callFrame": {"functionName": "fire", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2476}, "hitCount": 0, "children": [22]}, {"id": 22, "callFrame": {"functionName": "B", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2286}, "hitCount": 0, "children": [23, 150, 170]}, {"id": 23, "callFrame": {"functionName": "", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 2114, "columnNumber": 60860}, "hitCount": 0, "children": [24]}, {"id": 24, "callFrame": {"functionName": "_handlePathChanged", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 2114, "columnNumber": 60017}, "hitCount": 2, "children": [25, 67, 78, 88, 95, 109], "positionTicks": [{"line": 2115, "ticks": 2}]}, {"id": 25, "callFrame": {"functionName": "fire", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2476}, "hitCount": 0, "children": [26]}, {"id": 26, "callFrame": {"functionName": "B", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2286}, "hitCount": 0, "children": [27]}, {"id": 27, "callFrame": {"functionName": "", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 2114, "columnNumber": 93297}, "hitCount": 0, "children": [28]}, {"id": 28, "callFrame": {"functionName": "_handlePathCreated", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 2114, "columnNumber": 100185}, "hitCount": 1, "children": [29, 81, 91], "positionTicks": [{"line": 2115, "ticks": 1}]}, {"id": 29, "callFrame": {"functionName": "insert", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 2114, "columnNumber": 54143}, "hitCount": 0, "children": [30]}, {"id": 30, "callFrame": {"functionName": "fire", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2476}, "hitCount": 0, "children": [31]}, {"id": 31, "callFrame": {"functionName": "C", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2397}, "hitCount": 0, "children": [32]}, {"id": 32, "callFrame": {"functionName": "B", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2286}, "hitCount": 0, "children": [33, 68]}, {"id": 33, "callFrame": {"functionName": "", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 2114, "columnNumber": 89688}, "hitCount": 0, "children": [34]}, {"id": 34, "callFrame": {"functionName": "fire", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2476}, "hitCount": 0, "children": [35]}, {"id": 35, "callFrame": {"functionName": "C", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2397}, "hitCount": 0, "children": [36]}, {"id": 36, "callFrame": {"functionName": "B", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2286}, "hitCount": 0, "children": [37]}, {"id": 37, "callFrame": {"functionName": "", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 2094, "columnNumber": 447}, "hitCount": 1, "children": [38, 112], "positionTicks": [{"line": 2095, "ticks": 1}]}, {"id": 38, "callFrame": {"functionName": "_reportSyncingStatus", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 2094, "columnNumber": 1415}, "hitCount": 0, "children": [39, 72]}, {"id": 39, "callFrame": {"functionName": "fire", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2476}, "hitCount": 0, "children": [40]}, {"id": 40, "callFrame": {"functionName": "C", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2397}, "hitCount": 0, "children": [41]}, {"id": 41, "callFrame": {"functionName": "B", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2286}, "hitCount": 0, "children": [42, 63, 92]}, {"id": 42, "callFrame": {"functionName": "Ad", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 100, "columnNumber": 12945}, "hitCount": 0, "children": [43]}, {"id": 43, "callFrame": {"functionName": "jt", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 100, "columnNumber": 12459}, "hitCount": 0, "children": [44]}, {"id": 44, "callFrame": {"functionName": "r.leading", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 841, "columnNumber": 27846}, "hitCount": 0, "children": [45]}, {"id": 45, "callFrame": {"functionName": "setFolders", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 841, "columnNumber": 27117}, "hitCount": 0, "children": [46]}, {"id": 46, "callFrame": {"functionName": "", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 841, "columnNumber": 27162}, "hitCount": 0, "children": [47]}, {"id": 47, "callFrame": {"functionName": "set", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 841, "columnNumber": 24527}, "hitCount": 0, "children": [48, 60]}, {"id": 48, "callFrame": {"functionName": "k", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 0, "columnNumber": 3765}, "hitCount": 0, "children": [49]}, {"id": 49, "callFrame": {"functionName": "h", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 0, "columnNumber": 3364}, "hitCount": 0, "children": [50]}, {"id": 50, "callFrame": {"functionName": "p", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 0, "columnNumber": 3299}, "hitCount": 0, "children": [51]}, {"id": 51, "callFrame": {"functionName": "qL.dumpContext.leading", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 841, "columnNumber": 23874}, "hitCount": 0, "children": [52]}, {"id": 52, "callFrame": {"functionName": "save", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 635, "columnNumber": 38486}, "hitCount": 0, "children": [53]}, {"id": 53, "callFrame": {"functionName": "_ensureStorageUriExists", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 635, "columnNumber": 39126}, "hitCount": 0, "children": [54, 56]}, {"id": 54, "callFrame": {"functionName": "_getStorageUri", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 635, "columnNumber": 39212}, "hitCount": 0, "children": [55]}, {"id": 55, "callFrame": {"functionName": "joinPath", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 27, "columnNumber": 58082}, "hitCount": 2, "positionTicks": [{"line": 28, "ticks": 2}]}, {"id": 56, "callFrame": {"functionName": "Xl", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 635, "columnNumber": 36282}, "hitCount": 0, "children": [57]}, {"id": 57, "callFrame": {"functionName": "fn", "scriptId": "13", "url": "node:internal/util", "lineNumber": 446, "columnNumber": 13}, "hitCount": 0, "children": [58]}, {"id": 58, "callFrame": {"functionName": "t.stat", "scriptId": "77", "url": "node:electron/js2c/node_init", "lineNumber": 1, "columnNumber": 5437}, "hitCount": 0, "children": [59]}, {"id": 59, "callFrame": {"functionName": "stat", "scriptId": "0", "url": "", "lineNumber": -1, "columnNumber": -1}, "hitCount": 2, "positionTicks": [{"line": 2, "ticks": 2}]}, {"id": 60, "callFrame": {"functionName": "add", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 841, "columnNumber": 19031}, "hitCount": 0, "children": [61]}, {"id": 61, "callFrame": {"functionName": "", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 841, "columnNumber": 8775}, "hitCount": 0, "children": [62]}, {"id": 62, "callFrame": {"functionName": "get", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 841, "columnNumber": 7710}, "hitCount": 2, "positionTicks": [{"line": 842, "ticks": 2}]}, {"id": 63, "callFrame": {"functionName": "k", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 0, "columnNumber": 3765}, "hitCount": 0, "children": [64]}, {"id": 64, "callFrame": {"functionName": "p", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 0, "columnNumber": 3299}, "hitCount": 0, "children": [65]}, {"id": 65, "callFrame": {"functionName": "LO.sendSyncStatus.leading", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 1984, "columnNumber": 16901}, "hitCount": 0, "children": [66]}, {"id": 66, "callFrame": {"functionName": "postMessage", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 146, "columnNumber": 89047}, "hitCount": 1, "positionTicks": [{"line": 147, "ticks": 1}]}, {"id": 92, "callFrame": {"functionName": "", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 2093, "columnNumber": 12074}, "hitCount": 0, "children": [93]}, {"id": 93, "callFrame": {"functionName": "_handleSyncingProgress", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 2093, "columnNumber": 12410}, "hitCount": 1, "children": [94], "positionTicks": [{"line": 2094, "ticks": 1}]}, {"id": 94, "callFrame": {"functionName": "handleShowingSummaryMsg", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 2093, "columnNumber": 12543}, "hitCount": 1, "positionTicks": [{"line": 2094, "ticks": 1}]}, {"id": 72, "callFrame": {"functionName": "get currentFlags", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 640, "columnNumber": 5103}, "hitCount": 0, "children": [73]}, {"id": 73, "callFrame": {"functionName": "MH", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 287, "columnNumber": 10123}, "hitCount": 1, "children": [74, 111, 120, 143], "positionTicks": [{"line": 288, "ticks": 1}]}, {"id": 74, "callFrame": {"functionName": "iLr", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 287, "columnNumber": 3286}, "hitCount": 1, "children": [75], "positionTicks": [{"line": 288, "ticks": 1}]}, {"id": 75, "callFrame": {"functionName": "", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 287, "columnNumber": 10715}, "hitCount": 0, "children": [76, 105]}, {"id": 76, "callFrame": {"functionName": "fLr", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 287, "columnNumber": 3820}, "hitCount": 0, "children": [77]}, {"id": 77, "callFrame": {"functionName": "cLr", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 287, "columnNumber": 3592}, "hitCount": 2, "positionTicks": [{"line": 288, "ticks": 2}]}, {"id": 105, "callFrame": {"functionName": "MH", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 287, "columnNumber": 10123}, "hitCount": 1, "children": [106, 149], "positionTicks": [{"line": 288, "ticks": 1}]}, {"id": 106, "callFrame": {"functionName": "iLr", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 287, "columnNumber": 3286}, "hitCount": 0, "children": [107]}, {"id": 107, "callFrame": {"functionName": "", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 287, "columnNumber": 10715}, "hitCount": 0, "children": [108]}, {"id": 108, "callFrame": {"functionName": "fLr", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 287, "columnNumber": 3820}, "hitCount": 1, "positionTicks": [{"line": 288, "ticks": 1}]}, {"id": 149, "callFrame": {"functionName": "PRt", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 0, "columnNumber": 2155}, "hitCount": 1, "positionTicks": [{"line": 1, "ticks": 1}]}, {"id": 111, "callFrame": {"functionName": "e2r", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 192, "columnNumber": 14301}, "hitCount": 1, "children": [113], "positionTicks": [{"line": 193, "ticks": 1}]}, {"id": 113, "callFrame": {"functionName": "m0r", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 192, "columnNumber": 9603}, "hitCount": 0, "children": [114]}, {"id": 114, "callFrame": {"functionName": "Khr", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 192, "columnNumber": 14172}, "hitCount": 0, "children": [115]}, {"id": 115, "callFrame": {"functionName": "Qhr", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 192, "columnNumber": 13831}, "hitCount": 0, "children": [116]}, {"id": 116, "callFrame": {"functionName": "", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 192, "columnNumber": 13584}, "hitCount": 2, "positionTicks": [{"line": 193, "ticks": 2}]}, {"id": 120, "callFrame": {"functionName": "b7", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 192, "columnNumber": 6493}, "hitCount": 0, "children": [121]}, {"id": 121, "callFrame": {"functionName": "m7", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 192, "columnNumber": 1252}, "hitCount": 1, "positionTicks": [{"line": 193, "ticks": 1}]}, {"id": 143, "callFrame": {"functionName": "wfr", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 192, "columnNumber": 6166}, "hitCount": 1, "positionTicks": [{"line": 193, "ticks": 1}]}, {"id": 112, "callFrame": {"functionName": "_updateFolderState", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 2094, "columnNumber": 1135}, "hitCount": 1, "positionTicks": [{"line": 2095, "ticks": 1}]}, {"id": 68, "callFrame": {"functionName": "", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 2114, "columnNumber": 89611}, "hitCount": 0, "children": [69]}, {"id": 69, "callFrame": {"functionName": "fire", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2476}, "hitCount": 0, "children": [70]}, {"id": 70, "callFrame": {"functionName": "B", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2286}, "hitCount": 0, "children": [71]}, {"id": 71, "callFrame": {"functionName": "", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 2065, "columnNumber": 10759}, "hitCount": 2, "positionTicks": [{"line": 2066, "ticks": 2}]}, {"id": 81, "callFrame": {"functionName": "_emitFileNotification", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 2114, "columnNumber": 103829}, "hitCount": 0, "children": [82]}, {"id": 82, "callFrame": {"functionName": "fire", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2476}, "hitCount": 0, "children": [83]}, {"id": 83, "callFrame": {"functionName": "C", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2397}, "hitCount": 0, "children": [84]}, {"id": 84, "callFrame": {"functionName": "B", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2286}, "hitCount": 0, "children": [85]}, {"id": 85, "callFrame": {"functionName": "n", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 841, "columnNumber": 27972}, "hitCount": 0, "children": [86]}, {"id": 86, "callFrame": {"functionName": "add", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 841, "columnNumber": 19031}, "hitCount": 0, "children": [87]}, {"id": 87, "callFrame": {"functionName": "", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 841, "columnNumber": 8775}, "hitCount": 1, "positionTicks": [{"line": 842, "ticks": 1}]}, {"id": 91, "callFrame": {"functionName": "enqueueSerializedOperation", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 2114, "columnNumber": 67774}, "hitCount": 1, "positionTicks": [{"line": 2115, "ticks": 1}]}, {"id": 67, "callFrame": {"functionName": "VE", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 635, "columnNumber": 32716}, "hitCount": 1, "positionTicks": [{"line": 636, "ticks": 1}]}, {"id": 78, "callFrame": {"functionName": "t.lstatSync", "scriptId": "77", "url": "node:electron/js2c/node_init", "lineNumber": 1, "columnNumber": 4496}, "hitCount": 2, "children": [79], "positionTicks": [{"line": 2, "ticks": 2}]}, {"id": 79, "callFrame": {"functionName": "lstatSync", "scriptId": "45", "url": "node:fs", "lineNumber": 1710, "columnNumber": 18}, "hitCount": 0, "children": [80]}, {"id": 80, "callFrame": {"functionName": "lstat", "scriptId": "0", "url": "", "lineNumber": -1, "columnNumber": -1}, "hitCount": 10, "positionTicks": [{"line": 1711, "ticks": 10}]}, {"id": 88, "callFrame": {"functionName": "getPathInfo", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 2101, "columnNumber": 3033}, "hitCount": 0, "children": [89, 122]}, {"id": 89, "callFrame": {"functionName": "_getIgnoreStack", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 2101, "columnNumber": 3480}, "hitCount": 3, "children": [90, 118], "positionTicks": [{"line": 2102, "ticks": 3}]}, {"id": 90, "callFrame": {"functionName": "dirname", "scriptId": "36", "url": "node:path", "lineNumber": 731, "columnNumber": 9}, "hitCount": 1, "positionTicks": [{"line": 739, "ticks": 1}]}, {"id": 118, "callFrame": {"functionName": "isAbsolute", "scriptId": "36", "url": "node:path", "lineNumber": 444, "columnNumber": 12}, "hitCount": 1, "positionTicks": [{"line": 445, "ticks": 1}]}, {"id": 122, "callFrame": {"functionName": "getPathInfo", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 2094, "columnNumber": 16282}, "hitCount": 0, "children": [123, 138]}, {"id": 123, "callFrame": {"functionName": "hl", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 635, "columnNumber": 33141}, "hitCount": 0, "children": [124]}, {"id": 124, "callFrame": {"functionName": "j4", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 635, "columnNumber": 33026}, "hitCount": 0, "children": [125]}, {"id": 125, "callFrame": {"functionName": "H1", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 635, "columnNumber": 32825}, "hitCount": 0, "children": [126]}, {"id": 126, "callFrame": {"functionName": "relative", "scriptId": "36", "url": "node:path", "lineNumber": 538, "columnNumber": 10}, "hitCount": 0, "children": [127]}, {"id": 127, "callFrame": {"functionName": "resolve", "scriptId": "36", "url": "node:path", "lineNumber": 183, "columnNumber": 9}, "hitCount": 1, "positionTicks": [{"line": 78, "ticks": 1}]}, {"id": 138, "callFrame": {"functionName": "test", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 324, "columnNumber": 1117413}, "hitCount": 0, "children": [139]}, {"id": 139, "callFrame": {"functionName": "_test", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 324, "columnNumber": 1116894}, "hitCount": 0, "children": [140, 144]}, {"id": 140, "callFrame": {"functionName": "_t", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 324, "columnNumber": 1116994}, "hitCount": 0, "children": [141]}, {"id": 141, "callFrame": {"functionName": "_testOne", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 324, "columnNumber": 1116728}, "hitCount": 0, "children": [142]}, {"id": 142, "callFrame": {"functionName": "", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 324, "columnNumber": 1116775}, "hitCount": 1, "positionTicks": [{"line": 325, "ticks": 1}]}, {"id": 144, "callFrame": {"functionName": "e", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 324, "columnNumber": 1117666}, "hitCount": 1, "positionTicks": [{"line": 325, "ticks": 1}]}, {"id": 95, "callFrame": {"functionName": "t.<computed>", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 287, "columnNumber": 1027}, "hitCount": 0, "children": [96]}, {"id": 96, "callFrame": {"functionName": "value", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 285, "columnNumber": 2424}, "hitCount": 0, "children": [97]}, {"id": 97, "callFrame": {"functionName": "Ua.write", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 281, "columnNumber": 35350}, "hitCount": 0, "children": [98]}, {"id": 98, "callFrame": {"functionName": "vkr", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 281, "columnNumber": 36527}, "hitCount": 0, "children": [99]}, {"id": 99, "callFrame": {"functionName": "_3e", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 281, "columnNumber": 36954}, "hitCount": 0, "children": [100]}, {"id": 100, "callFrame": {"functionName": "Pm._write", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 282, "columnNumber": 11040}, "hitCount": 0, "children": [101]}, {"id": 101, "callFrame": {"functionName": "Pm._read", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 282, "columnNumber": 11284}, "hitCount": 0, "children": [102]}, {"id": 102, "callFrame": {"functionName": "_transform", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 286, "columnNumber": 972}, "hitCount": 0, "children": [103, 128]}, {"id": 103, "callFrame": {"functionName": "", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 280, "columnNumber": 12179}, "hitCount": 2, "children": [104], "positionTicks": [{"line": 281, "ticks": 2}]}, {"id": 104, "callFrame": {"functionName": "W", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 280, "columnNumber": 9915}, "hitCount": 2, "positionTicks": [{"line": 281, "ticks": 2}]}, {"id": 128, "callFrame": {"functionName": "Blt", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 281, "columnNumber": 21373}, "hitCount": 0, "children": [129]}, {"id": 129, "callFrame": {"functionName": "l3e", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 281, "columnNumber": 21950}, "hitCount": 0, "children": [130]}, {"id": 130, "callFrame": {"functionName": "emit", "scriptId": "22", "url": "node:events", "lineNumber": 464, "columnNumber": 43}, "hitCount": 0, "children": [131]}, {"id": 131, "callFrame": {"functionName": "u", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 281, "columnNumber": 25878}, "hitCount": 0, "children": [132]}, {"id": 132, "callFrame": {"functionName": "Ua.write", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 281, "columnNumber": 35350}, "hitCount": 0, "children": [133]}, {"id": 133, "callFrame": {"functionName": "vkr", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 281, "columnNumber": 36527}, "hitCount": 0, "children": [134]}, {"id": 134, "callFrame": {"functionName": "_3e", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 281, "columnNumber": 36954}, "hitCount": 0, "children": [135]}, {"id": 135, "callFrame": {"functionName": "XD._write", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 282, "columnNumber": 813}, "hitCount": 0, "children": [136]}, {"id": 136, "callFrame": {"functionName": "log", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 635, "columnNumber": 30654}, "hitCount": 0, "children": [137, 145]}, {"id": 137, "callFrame": {"functionName": "trace", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 126, "columnNumber": 3060}, "hitCount": 1, "positionTicks": [{"line": 127, "ticks": 1}]}, {"id": 145, "callFrame": {"functionName": "KD.onwrite", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 281, "columnNumber": 33717}, "hitCount": 0, "children": [146]}, {"id": 146, "callFrame": {"functionName": "wkr", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 281, "columnNumber": 37393}, "hitCount": 0, "children": [147]}, {"id": 147, "callFrame": {"functionName": "", "scriptId": "117", "url": "node:electron/js2c/utility_init", "lineNumber": 1, "columnNumber": 14992}, "hitCount": 0, "children": [148]}, {"id": 148, "callFrame": {"functionName": "", "scriptId": "0", "url": "", "lineNumber": -1, "columnNumber": -1}, "hitCount": 1, "positionTicks": [{"line": 2, "ticks": 1}]}, {"id": 109, "callFrame": {"functionName": "H1", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 635, "columnNumber": 32825}, "hitCount": 0, "children": [110]}, {"id": 110, "callFrame": {"functionName": "relative", "scriptId": "36", "url": "node:path", "lineNumber": 538, "columnNumber": 10}, "hitCount": 1, "positionTicks": [{"line": 553, "ticks": 1}]}, {"id": 150, "callFrame": {"functionName": "", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 2114, "columnNumber": 60908}, "hitCount": 0, "children": [151]}, {"id": 151, "callFrame": {"functionName": "_handlePathChanged", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 2114, "columnNumber": 60017}, "hitCount": 1, "children": [152, 155, 164], "positionTicks": [{"line": 2115, "ticks": 1}]}, {"id": 152, "callFrame": {"functionName": "t.lstatSync", "scriptId": "77", "url": "node:electron/js2c/node_init", "lineNumber": 1, "columnNumber": 4496}, "hitCount": 0, "children": [153]}, {"id": 153, "callFrame": {"functionName": "lstatSync", "scriptId": "45", "url": "node:fs", "lineNumber": 1710, "columnNumber": 18}, "hitCount": 0, "children": [154]}, {"id": 154, "callFrame": {"functionName": "lstat", "scriptId": "0", "url": "", "lineNumber": -1, "columnNumber": -1}, "hitCount": 12, "positionTicks": [{"line": 1711, "ticks": 12}]}, {"id": 155, "callFrame": {"functionName": "t.<computed>", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 287, "columnNumber": 1027}, "hitCount": 0, "children": [156]}, {"id": 156, "callFrame": {"functionName": "value", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 285, "columnNumber": 2424}, "hitCount": 1, "children": [157], "positionTicks": [{"line": 286, "ticks": 1}]}, {"id": 157, "callFrame": {"functionName": "Ua.write", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 281, "columnNumber": 35350}, "hitCount": 0, "children": [158]}, {"id": 158, "callFrame": {"functionName": "vkr", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 281, "columnNumber": 36527}, "hitCount": 0, "children": [159]}, {"id": 159, "callFrame": {"functionName": "_3e", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 281, "columnNumber": 36954}, "hitCount": 0, "children": [160]}, {"id": 160, "callFrame": {"functionName": "Pm._write", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 282, "columnNumber": 11040}, "hitCount": 0, "children": [161]}, {"id": 161, "callFrame": {"functionName": "Pm._read", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 282, "columnNumber": 11284}, "hitCount": 0, "children": [162]}, {"id": 162, "callFrame": {"functionName": "_transform", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 286, "columnNumber": 972}, "hitCount": 0, "children": [163]}, {"id": 163, "callFrame": {"functionName": "", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 280, "columnNumber": 12179}, "hitCount": 1, "positionTicks": [{"line": 281, "ticks": 1}]}, {"id": 164, "callFrame": {"functionName": "fire", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2476}, "hitCount": 0, "children": [165]}, {"id": 165, "callFrame": {"functionName": "B", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2286}, "hitCount": 0, "children": [166]}, {"id": 166, "callFrame": {"functionName": "", "scriptId": "371", "url": "file:///c:/Users/<USER>/.vscode/extensions/augment.vscode-augment-0.509.1/out/extension.js", "lineNumber": 2114, "columnNumber": 93382}, "hitCount": 0, "children": [167]}, {"id": 167, "callFrame": {"functionName": "fire", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2476}, "hitCount": 0, "children": [168]}, {"id": 168, "callFrame": {"functionName": "C", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2397}, "hitCount": 0, "children": [169]}, {"id": 169, "callFrame": {"functionName": "B", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 26, "columnNumber": 2286}, "hitCount": 1, "positionTicks": [{"line": 27, "ticks": 1}]}, {"id": 170, "callFrame": {"functionName": "", "scriptId": "418", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/extensions/extension-editing/dist/extensionEditingMain.js", "lineNumber": 0, "columnNumber": 275874}, "hitCount": 0, "children": [171]}, {"id": 171, "callFrame": {"functionName": "packageJsonChanged", "scriptId": "418", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/extensions/extension-editing/dist/extensionEditingMain.js", "lineNumber": 0, "columnNumber": 285296}, "hitCount": 0, "children": [172]}, {"id": 172, "callFrame": {"functionName": "", "scriptId": "418", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/extensions/extension-editing/dist/extensionEditingMain.js", "lineNumber": 0, "columnNumber": 285419}, "hitCount": 0, "children": [173]}, {"id": 173, "callFrame": {"functionName": "getUriFolder", "scriptId": "418", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/extensions/extension-editing/dist/extensionEditingMain.js", "lineNumber": 0, "columnNumber": 285524}, "hitCount": 0, "children": [174]}, {"id": 174, "callFrame": {"functionName": "with", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 27, "columnNumber": 57233}, "hitCount": 1, "positionTicks": [{"line": 28, "ticks": 1}]}, {"id": 175, "callFrame": {"functionName": "get fsPath", "scriptId": "149", "url": "file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js", "lineNumber": 27, "columnNumber": 58976}, "hitCount": 1, "positionTicks": [{"line": 28, "ticks": 1}]}, {"id": 176, "callFrame": {"functionName": "postMessage", "scriptId": "117", "url": "node:electron/js2c/utility_init", "lineNumber": 1, "columnNumber": 3178}, "hitCount": 1, "positionTicks": [{"line": 2, "ticks": 1}]}, {"id": 117, "callFrame": {"functionName": "(garbage collector)", "scriptId": "0", "url": "", "lineNumber": -1, "columnNumber": -1}, "hitCount": 3}, {"id": 119, "callFrame": {"functionName": "(program)", "scriptId": "0", "url": "", "lineNumber": -1, "columnNumber": -1}, "hitCount": 1}, {"id": 177, "callFrame": {"functionName": "processTicksAndRejections", "scriptId": "30", "url": "node:internal/process/task_queues", "lineNumber": 71, "columnNumber": 34}, "hitCount": 0, "children": [178]}, {"id": 178, "callFrame": {"functionName": "runMicrotasks", "scriptId": "0", "url": "", "lineNumber": -1, "columnNumber": -1}, "hitCount": 0, "children": [179]}, {"id": 179, "callFrame": {"functionName": "_triggerOnFileChange", "scriptId": "402", "url": "file:///c:/Users/<USER>/.vscode/extensions/github.copilot-1.346.0/dist/extension.js", "lineNumber": 3774, "columnNumber": 14357}, "hitCount": 0, "children": [180]}, {"id": 180, "callFrame": {"functionName": "", "scriptId": "402", "url": "file:///c:/Users/<USER>/.vscode/extensions/github.copilot-1.346.0/dist/extension.js", "lineNumber": 926, "columnNumber": 9191}, "hitCount": 0, "children": [181]}, {"id": 181, "callFrame": {"functionName": "match", "scriptId": "402", "url": "file:///c:/Users/<USER>/.vscode/extensions/github.copilot-1.346.0/dist/extension.js", "lineNumber": 928, "columnNumber": 1682}, "hitCount": 2, "positionTicks": [{"line": 929, "ticks": 2}]}], "startTime": 1813966872, "endTime": 1816699970, "samples": [55, 55, 55, 59, 59, 62, 62, 66, 67, 71, 77, 80, 87, 90, 80, 74, 91, 94, 104, 108, 89, 110, 111, 24, 80, 28, 112, 89, 89, 80, 103, 80, 78, 116, 117, 117, 117, 80, 24, 73, 118, 80, 105, 121, 103, 127, 137, 77, 142, 71, 80, 80, 78, 143, 80, 37, 104, 144, 93, 148, 149, 116, 154, 163, 154, 154, 154, 154, 154, 154, 154, 154, 154, 156, 151, 154, 154, 169, 174, 175, 176, 181, 181], "timeDeltas": [2668613, 1232, 1059, 724, 139, 650, 710, 692, 327, 705, 181, 483, 741, 631, 705, 688, 702, 768, 707, 291, 656, 683, 708, 697, 678, 700, 671, 677, 687, 688, 658, 828, 1992, 808, 413, 666, 671, 739, 695, 856, 709, 689, 1308, 683, 682, 687, 214, 180, 687, 1413, 737, 1147, 692, 713, 731, 698, 682, 697, 589, 705, 215, 686, 686, 690, 207, 690, 3836, 737, 823, 671, 724, 686, 698, 669, 688, 692, 709, 667, 690, 165, 661, 4023, 1647]}