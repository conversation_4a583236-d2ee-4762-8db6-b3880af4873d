2025-07-25 18:24:48.727 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vsliveshare.vsliveshare' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-25 18:24:48.745 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.gather' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-25 18:24:48.746 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.vscode-pylance' wants API proposal 'mcpConfigurationProvider' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-07-25 18:24:50.813 [info] ComputeTargetPlatform: win32-x64
2025-07-25 18:24:50.852 [info] Started local extension host with pid 12964.
2025-07-25 18:24:58.485 [info] Settings Sync: Account status changed from uninitialized to unavailable
2025-07-25 18:24:59.095 [info] Extension host (LocalProcess pid: 12964) is unresponsive.
2025-07-25 18:25:00.903 [info] [perf] Render performance baseline is 182ms
2025-07-25 18:25:01.918 [info] Extension host (LocalProcess pid: 12964) is responsive.
2025-07-25 18:25:08.588 [info] Extension host (LocalProcess pid: 12964) is unresponsive.
2025-07-25 18:25:11.069 [info] UNRESPONSIVE extension host: starting to profile NOW
2025-07-25 18:25:13.806 [error] [Extension Host] (node:12964) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `Code --trace-deprecation ...` to show where the warning was created)
2025-07-25 18:25:16.171 [info] Started local extension host with pid 10704.
2025-07-25 18:25:17.828 [warning] UNRESPONSIVE extension host: 'github.copilot' took 32.**************% of 1715.426ms, saved PROFILE here: 'file:///c%3A/Users/<USER>/AppData/Local/Temp/exthost-b0b440.cpuprofile'
2025-07-25 18:25:28.295 [error] [Extension Host] (node:10704) [DEP0040] DeprecationWarning: The `punycode` module is deprecated. Please use a userland alternative instead.
(Use `Code --trace-deprecation ...` to show where the warning was created)
2025-07-25 18:25:36.678 [info] Extension host (LocalProcess pid: 10704) is unresponsive.
2025-07-25 18:25:38.338 [info] UNRESPONSIVE extension host: starting to profile NOW
2025-07-25 18:25:38.722 [info] Extension host (LocalProcess pid: 10704) is responsive.
2025-07-25 18:25:38.722 [info] UNRESPONSIVE extension host: received responsive event and cancelling profiling session
2025-07-25 18:25:39.235 [warning] UNRESPONSIVE extension host: 'vscode.merge-conflict' took 12.987304942364489% of 50.261ms, saved PROFILE here: 'file:///c%3A/Users/<USER>/AppData/Local/Temp/exthost-3de264.cpuprofile'
2025-07-25 18:41:56.455 [error] [LanguageModelToolsService#invokeTool] Error from tool copilot_createFile with parameters {"filePath":"c:\\Users\\<USER>\\Desktop\\augment_code-vip\\build_exe.bat","content":"@echo off\nREM Build AugmentCleanerV2 as a standalone Windows EXE using PyInstaller\n\nREM Ensure PyInstaller is installed\npip install pyinstaller\n\nREM Build the EXE (onefile, console, add version info if needed)\npyinstaller --onefile --clean --name AugmentCleanerV2 augment_cleaner_v2.py\n\necho.\necho Build complete. EXE is in the 'dist' folder.\npause\n"}:
File already exists. You must use an edit tool to modify it.: Error: File already exists. You must use an edit tool to modify it.
    at VK.invoke (c:\Users\<USER>\.vscode\extensions\github.copilot-chat-0.28.5\dist\extension.js:1402:2582)
    at Jq.$invokeTool (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:153:2696)
