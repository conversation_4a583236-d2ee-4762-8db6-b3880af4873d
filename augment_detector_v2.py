import os
import sqlite3
import json
import subprocess
import platform
import sys
from datetime import datetime
import winreg

def detect_augment_versions():
    """Detect all versions of Augment Code and their data collection patterns"""
    print("🔍 Advanced Augment Detection - Version 2.0")
    print("=" * 60)
    
    findings = {
        'extensions': [],
        'databases': [],
        'config_files': [],
        'cache_data': [],
        'registry_entries': [],
        'network_traces': [],
        'telemetry_data': [],
        'user_data': [],
        'version_info': []
    }
    
    # Check for different Augment extension patterns
    extension_patterns = [
        'augment',
        'augment-code',
        'augmentcode',
        'augment.vscode-augment',
        'augmentcode.augment-code',
        'augment.augment-code'
        
    ]
    
    # VSCode installation paths
    vscode_paths = [
        os.path.expandvars(r"%APPDATA%\Code"),
        os.path.expandvars(r"%APPDATA%\Code - Insiders"),
        os.path.expandvars(r"%APPDATA%\Cursor"),
        os.path.expanduser("~/.vscode"),
        os.path.expanduser("~/.vscode-insiders")
    ]
    
    print("🔍 Scanning for Augment installations...")
    
    for vscode_path in vscode_paths:
        if not os.path.exists(vscode_path):
            continue
            
        ide_name = os.path.basename(vscode_path)
        print(f"\n📁 Checking {ide_name}...")
        
        # Check extensions
        extensions_dir = os.path.join(vscode_path, "User", "extensions")
        if os.path.exists(extensions_dir):
            for item in os.listdir(extensions_dir):
                for pattern in extension_patterns:
                    if pattern in item.lower():
                        version = extract_version_from_extension(item)
                        findings['extensions'].append({
                            'ide': ide_name,
                            'name': item,
                            'path': os.path.join(extensions_dir, item),
                            'version': version,
                            'size': get_folder_size(os.path.join(extensions_dir, item))
                        })
                        print(f"   📦 Found: {item} (Version: {version})")
                        
                        # Analyze extension contents for newer versions
                        analyze_extension_contents(os.path.join(extensions_dir, item), findings)
        
        # Check global storage and state databases
        global_storage = os.path.join(vscode_path, "User", "globalStorage")
        if os.path.exists(global_storage):
            analyze_global_storage(global_storage, findings, ide_name)
        
        # Check workspace storage
        workspace_storage = os.path.join(vscode_path, "User", "workspaceStorage")
        if os.path.exists(workspace_storage):
            analyze_workspace_storage(workspace_storage, findings, ide_name)
        
        # Check settings and configuration
        settings_file = os.path.join(vscode_path, "User", "settings.json")
        if os.path.exists(settings_file):
            analyze_settings_file(settings_file, findings, ide_name)
    
    # Check system-wide data collection
    check_system_telemetry(findings)
    check_network_activity(findings)
    check_registry_data(findings)
    
    return findings

def extract_version_from_extension(extension_name):
    """Extract version number from extension folder name"""
    import re
    version_pattern = r'(\d+\.\d+\.\d+)'
    match = re.search(version_pattern, extension_name)
    return match.group(1) if match else "Unknown"

def get_folder_size(folder_path):
    """Calculate folder size in MB"""
    try:
        total_size = 0
        for dirpath, dirnames, filenames in os.walk(folder_path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total_size += os.path.getsize(filepath)
        return round(total_size / (1024 * 1024), 2)  # Convert to MB
    except:
        return 0

def analyze_extension_contents(extension_path, findings):
    """Analyze extension contents for data collection patterns"""
    try:
        # Check package.json for capabilities and permissions
        package_json = os.path.join(extension_path, "package.json")
        if os.path.exists(package_json):
            with open(package_json, 'r', encoding='utf-8') as f:
                package_data = json.load(f)
                
                # Check for telemetry and data collection
                if 'contributes' in package_data:
                    contributes = package_data['contributes']
                    if 'configuration' in contributes:
                        findings['config_files'].append({
                            'type': 'extension_config',
                            'path': package_json,
                            'data': contributes['configuration']
                        })
                
                # Check for network permissions
                if 'activationEvents' in package_data:
                    findings['telemetry_data'].append({
                        'type': 'activation_events',
                        'events': package_data['activationEvents']
                    })
        
        # Check for data files
        for root, dirs, files in os.walk(extension_path):
            for file in files:
                if file.endswith(('.db', '.sqlite', '.json', '.log')):
                    file_path = os.path.join(root, file)
                    findings['cache_data'].append({
                        'type': 'extension_data',
                        'path': file_path,
                        'size': os.path.getsize(file_path) if os.path.exists(file_path) else 0
                    })
    except Exception as e:
        print(f"   ❌ Error analyzing extension: {str(e)}")

def analyze_global_storage(global_storage_path, findings, ide_name):
    """Analyze VSCode global storage for Augment data"""
    try:
        state_db = os.path.join(global_storage_path, "state.vscdb")
        if os.path.exists(state_db):
            conn = sqlite3.connect(state_db)
            cur = conn.cursor()
            
            # Get all keys to analyze patterns
            cur.execute("SELECT key, value FROM ItemTable")
            all_items = cur.fetchall()
            
            augment_items = []
            for key, value in all_items:
                if any(pattern in key.lower() for pattern in ['augment', 'telemetry', 'analytics']):
                    augment_items.append({
                        'key': key,
                        'value': value[:100] + "..." if len(str(value)) > 100 else value,
                        'ide': ide_name
                    })
            
            if augment_items:
                findings['databases'].append({
                    'path': state_db,
                    'ide': ide_name,
                    'entries': len(augment_items),
                    'sample_data': augment_items[:5]  # First 5 entries
                })
                print(f"   🗄️ Found {len(augment_items)} database entries")
            
            conn.close()
    except Exception as e:
        print(f"   ❌ Error analyzing global storage: {str(e)}")

def analyze_workspace_storage(workspace_storage_path, findings, ide_name):
    """Analyze workspace storage for Augment data"""
    try:
        for workspace_dir in os.listdir(workspace_storage_path):
            workspace_path = os.path.join(workspace_storage_path, workspace_dir)
            if os.path.isdir(workspace_path):
                # Check for Augment-related workspace data
                for file in os.listdir(workspace_path):
                    if 'augment' in file.lower():
                        file_path = os.path.join(workspace_path, file)
                        findings['user_data'].append({
                            'type': 'workspace_data',
                            'path': file_path,
                            'ide': ide_name,
                            'workspace': workspace_dir
                        })
    except Exception as e:
        print(f"   ❌ Error analyzing workspace storage: {str(e)}")

def analyze_settings_file(settings_file, findings, ide_name):
    """Analyze VSCode settings for Augment configuration"""
    try:
        with open(settings_file, 'r', encoding='utf-8') as f:
            content = f.read()
            if 'augment' in content.lower():
                # Parse JSON to extract Augment settings
                try:
                    settings = json.loads(content)
                    augment_settings = {}
                    for key, value in settings.items():
                        if 'augment' in key.lower():
                            augment_settings[key] = value
                    
                    if augment_settings:
                        findings['config_files'].append({
                            'type': 'vscode_settings',
                            'path': settings_file,
                            'ide': ide_name,
                            'settings': augment_settings
                        })
                        print(f"   ⚙️ Found Augment settings in {ide_name}")
                except json.JSONDecodeError:
                    pass
    except Exception as e:
        print(f"   ❌ Error analyzing settings: {str(e)}")

def check_system_telemetry(findings):
    """Check for system-wide telemetry and data collection"""
    print("\n🌐 Checking system-wide telemetry...")

    # Check common telemetry locations
    telemetry_paths = [
        os.path.expandvars(r"%LOCALAPPDATA%\Augment"),
        os.path.expandvars(r"%APPDATA%\Augment"),
        os.path.expandvars(r"%TEMP%\Augment"),
        os.path.expanduser("~/.augment")
    ]

    for path in telemetry_paths:
        if os.path.exists(path):
            findings['telemetry_data'].append({
                'type': 'system_telemetry',
                'path': path,
                'size': get_folder_size(path)
            })
            print(f"   📊 Found telemetry data: {path}")

    # Check for deeper system information collection
    check_system_info_collection(findings)

def check_system_info_collection(findings):
    """Check for deep system information collection by newer Augment versions"""
    print("\n🔍 Checking for deep system information collection...")

    system_info_collected = {
        'user_identity': [],
        'hardware_info': [],
        'software_inventory': [],
        'network_config': [],
        'file_system_access': [],
        'process_monitoring': []
    }

    # Check for user identity collection
    print("   👤 Checking user identity data...")
    user_data_indicators = [
        os.environ.get('USERNAME', 'Unknown'),
        os.environ.get('COMPUTERNAME', 'Unknown'),
        os.environ.get('USERDOMAIN', 'Unknown'),
        os.environ.get('USERPROFILE', 'Unknown')
    ]

    # Check VSCode databases for stored user info
    vscode_paths = [
        os.path.expandvars(r"%APPDATA%\Code\User\globalStorage"),
        os.path.expandvars(r"%APPDATA%\Code - Insiders\User\globalStorage"),
        os.path.expandvars(r"%APPDATA%\Cursor\User\globalStorage")
    ]

    for vscode_path in vscode_paths:
        state_db = os.path.join(vscode_path, "state.vscdb")
        if os.path.exists(state_db):
            try:
                conn = sqlite3.connect(state_db)
                cur = conn.cursor()

                # Check for user identity data
                identity_patterns = [
                    '%username%', '%user%', '%computer%', '%machine%',
                    '%email%', '%account%', '%profile%', '%identity%'
                ]

                for pattern in identity_patterns:
                    cur.execute("SELECT key, value FROM ItemTable WHERE LOWER(key) LIKE ? OR LOWER(value) LIKE ?",
                              (pattern, pattern))
                    results = cur.fetchall()

                    for key, value in results:
                        # Check if actual username appears in the data
                        username = os.environ.get('USERNAME', '').lower()
                        if username and username in str(value).lower():
                            system_info_collected['user_identity'].append({
                                'type': 'username_in_data',
                                'key': key,
                                'contains_username': True,
                                'database': state_db
                            })
                            print(f"   🚨 Found username in database key: {key}")

                # Check for hardware fingerprinting
                hardware_patterns = [
                    '%cpu%', '%processor%', '%memory%', '%disk%', '%gpu%',
                    '%hardware%', '%system%', '%machine%', '%device%'
                ]

                for pattern in hardware_patterns:
                    cur.execute("SELECT key, value FROM ItemTable WHERE LOWER(key) LIKE ?", (pattern,))
                    results = cur.fetchall()

                    for key, value in results:
                        system_info_collected['hardware_info'].append({
                            'type': 'hardware_fingerprint',
                            'key': key,
                            'database': state_db
                        })
                        print(f"   🖥️ Found hardware info: {key}")

                conn.close()

            except Exception as e:
                print(f"   ❌ Error checking database: {str(e)}")

    # Check for file system access patterns
    print("   📁 Checking file system access patterns...")
    check_file_access_patterns(system_info_collected)

    # Check for network configuration collection
    print("   🌐 Checking network configuration collection...")
    check_network_config_collection(system_info_collected)

    # Check for software inventory
    print("   💿 Checking software inventory collection...")
    check_software_inventory(system_info_collected)

    findings['system_info_collection'] = system_info_collected

    # Summary of concerning findings
    total_concerning = sum(len(category) for category in system_info_collected.values())
    if total_concerning > 0:
        print(f"   🚨 PRIVACY CONCERN: Found {total_concerning} instances of deep system data collection!")
    else:
        print("   ✅ No concerning system data collection detected")

def check_file_access_patterns(system_info_collected):
    """Check for suspicious file access patterns"""
    # Check recent file access logs
    temp_dirs = [
        os.path.expandvars(r"%TEMP%"),
        os.path.expandvars(r"%LOCALAPPDATA%\Temp")
    ]

    for temp_dir in temp_dirs:
        if os.path.exists(temp_dir):
            try:
                for item in os.listdir(temp_dir):
                    if 'augment' in item.lower() and any(keyword in item.lower() for keyword in ['files', 'access', 'scan', 'index']):
                        system_info_collected['file_system_access'].append({
                            'type': 'file_access_log',
                            'path': os.path.join(temp_dir, item),
                            'suspicious': True
                        })
                        print(f"   📁 Suspicious file access pattern: {item}")
            except Exception:
                pass

def check_network_config_collection(system_info_collected):
    """Check for network configuration data collection"""
    try:
        # Check for network adapter information
        result = subprocess.run(['ipconfig', '/all'], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            # Check if this info might be collected by Augment
            network_indicators = ['adapter', 'ip', 'mac', 'dns', 'gateway']

            # Check VSCode logs for network info
            log_paths = [
                os.path.expandvars(r"%APPDATA%\Code\logs"),
                os.path.expandvars(r"%APPDATA%\Code - Insiders\logs")
            ]

            for log_path in log_paths:
                if os.path.exists(log_path):
                    try:
                        for root, dirs, files in os.walk(log_path):
                            for file in files:
                                if file.endswith('.log'):
                                    file_path = os.path.join(root, file)
                                    try:
                                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                                            content = f.read()
                                            if any(indicator in content.lower() for indicator in network_indicators):
                                                system_info_collected['network_config'].append({
                                                    'type': 'network_info_in_logs',
                                                    'path': file_path,
                                                    'suspicious': True
                                                })
                                                print(f"   🌐 Network info found in logs: {file}")
                                                break
                                    except Exception:
                                        pass
                    except Exception:
                        pass
    except Exception:
        pass

def check_software_inventory(system_info_collected):
    """Check for software inventory collection"""
    # Check if Augment is collecting installed software information
    try:
        # Check for software inventory in Augment data
        augment_paths = [
            os.path.expandvars(r"%LOCALAPPDATA%\Augment"),
            os.path.expandvars(r"%APPDATA%\Augment")
        ]

        for path in augment_paths:
            if os.path.exists(path):
                for root, dirs, files in os.walk(path):
                    for file in files:
                        if any(keyword in file.lower() for keyword in ['software', 'programs', 'installed', 'inventory']):
                            system_info_collected['software_inventory'].append({
                                'type': 'software_inventory',
                                'path': os.path.join(root, file),
                                'suspicious': True
                            })
                            print(f"   💿 Software inventory file: {file}")
    except Exception:
        pass

def check_network_activity(findings):
    """Check for network activity and API endpoints"""
    print("\n🌐 Checking network traces...")
    
    # Check hosts file
    hosts_file = r"C:\Windows\System32\drivers\etc\hosts"
    try:
        if os.path.exists(hosts_file):
            with open(hosts_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                if 'augment' in content.lower():
                    findings['network_traces'].append({
                        'type': 'hosts_file',
                        'path': hosts_file,
                        'content': 'Contains Augment entries'
                    })
                    print("   🌐 Found Augment entries in hosts file")
    except Exception as e:
        print(f"   ❌ Error checking hosts file: {str(e)}")

def check_registry_data(findings):
    """Check Windows Registry for Augment data"""
    print("\n🗂️ Checking Windows Registry...")
    
    try:
        registry_paths = [
            (winreg.HKEY_CURRENT_USER, r"Software\Microsoft\Windows\CurrentVersion\Uninstall"),
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall"),
            (winreg.HKEY_CURRENT_USER, r"Software"),
        ]
        
        for hkey, path in registry_paths:
            try:
                with winreg.OpenKey(hkey, path) as key:
                    i = 0
                    while True:
                        try:
                            subkey_name = winreg.EnumKey(key, i)
                            if 'augment' in subkey_name.lower():
                                findings['registry_entries'].append({
                                    'hkey': 'HKEY_CURRENT_USER' if hkey == winreg.HKEY_CURRENT_USER else 'HKEY_LOCAL_MACHINE',
                                    'path': f"{path}\\{subkey_name}",
                                    'name': subkey_name
                                })
                                print(f"   📝 Found registry entry: {subkey_name}")
                            i += 1
                        except WindowsError:
                            break
            except Exception:
                continue
    except Exception as e:
        print(f"   ❌ Error checking registry: {str(e)}")

def generate_report(findings):
    """Generate detailed report of findings"""
    print("\n" + "=" * 60)
    print("📊 AUGMENT DETECTION REPORT")
    print("=" * 60)
    
    total_items = sum(len(findings[key]) for key in findings)
    print(f"🎯 Total items found: {total_items}")
    
    for category, items in findings.items():
        if items:
            print(f"\n📋 {category.upper().replace('_', ' ')} ({len(items)} items):")
            for item in items[:3]:  # Show first 3 items
                if isinstance(item, dict):
                    if 'version' in item:
                        print(f"   • Version {item['version']} - {item.get('name', 'Unknown')}")
                    elif 'path' in item:
                        print(f"   • {os.path.basename(item['path'])}")
                    else:
                        print(f"   • {str(item)[:50]}...")
            if len(items) > 3:
                print(f"   ... and {len(items) - 3} more")
    
    return findings

def analyze_newer_version_patterns(findings):
    """Analyze patterns specific to newer Augment versions (0.492.2+)"""
    print("\n🔍 Analyzing newer version patterns...")

    newer_version_indicators = {
        'enhanced_telemetry': [],
        'cloud_sync': [],
        'ai_features': [],
        'collaboration_data': [],
        'performance_metrics': []
    }

    # Check for newer version specific data collection
    for extension in findings['extensions']:
        version = extension.get('version', '0.0.0')
        if version and compare_versions(version, '0.490.0') >= 0:
            print(f"   🆕 Newer version detected: {version}")

            # Analyze newer version specific files
            ext_path = extension['path']

            # Check for AI/ML data collection
            ai_files = ['models', 'training_data', 'user_interactions']
            for ai_file in ai_files:
                ai_path = os.path.join(ext_path, ai_file)
                if os.path.exists(ai_path):
                    newer_version_indicators['ai_features'].append({
                        'type': ai_file,
                        'path': ai_path,
                        'size': get_folder_size(ai_path)
                    })

            # Check for cloud sync data
            cloud_files = ['sync', 'cloud_state', 'remote_config']
            for cloud_file in cloud_files:
                cloud_path = os.path.join(ext_path, cloud_file)
                if os.path.exists(cloud_path):
                    newer_version_indicators['cloud_sync'].append({
                        'type': cloud_file,
                        'path': cloud_path,
                        'size': get_folder_size(cloud_path)
                    })

    return newer_version_indicators

def compare_versions(version1, version2):
    """Compare two version strings"""
    try:
        v1_parts = [int(x) for x in version1.split('.')]
        v2_parts = [int(x) for x in version2.split('.')]

        # Pad shorter version with zeros
        max_len = max(len(v1_parts), len(v2_parts))
        v1_parts.extend([0] * (max_len - len(v1_parts)))
        v2_parts.extend([0] * (max_len - len(v2_parts)))

        for i in range(max_len):
            if v1_parts[i] > v2_parts[i]:
                return 1
            elif v1_parts[i] < v2_parts[i]:
                return -1
        return 0
    except:
        return 0

if __name__ == "__main__":
    try:
        findings = detect_augment_versions()

        # Analyze newer version patterns
        newer_patterns = analyze_newer_version_patterns(findings)
        findings.update(newer_patterns)

        report = generate_report(findings)

        # Save detailed report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"augment_detection_report_{timestamp}.json"

        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(findings, f, indent=2, default=str)

        print(f"\n💾 Detailed report saved to: {report_file}")

        # Show version-specific recommendations
        print("\n🎯 RECOMMENDATIONS:")
        for extension in findings['extensions']:
            version = extension.get('version', 'Unknown')
            if version != 'Unknown':
                if compare_versions(version, '0.490.0') >= 0:
                    print(f"   🆕 Version {version}: Newer version detected - enhanced cleaning needed")
                    print(f"      • Check for AI/ML data collection")
                    print(f"      • Look for cloud sync data")
                    print(f"      • Enhanced telemetry patterns")
                else:
                    print(f"   📦 Version {version}: Standard cleaning should work")

        print("\nPress Enter to exit...")
        input()

    except Exception as e:
        print(f"❌ Error: {str(e)}")
        print("Press Enter to exit...")
        input()
