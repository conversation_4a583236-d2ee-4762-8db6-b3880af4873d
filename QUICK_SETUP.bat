@echo off
title AUGMENT PRIVACY TOOLS - QUICK SETUP
color 0A

echo.
echo  ██████╗ ██████╗  ██████╗ ███████╗███████╗███████╗███████╗ ██████╗ ██████╗ 
echo  ██╔══██╗██╔══██╗██╔═══██╗██╔════╝██╔════╝██╔════╝██╔════╝██╔═══██╗██╔══██╗
echo  ██████╔╝██████╔╝██║   ██║█████╗  █████╗  ███████╗███████╗██║   ██║██████╔╝
echo  ██╔═══╝ ██╔══██╗██║   ██║██╔══╝  ██╔══╝  ╚════██║╚════██║██║   ██║██╔══██╗
echo  ██║     ██║  ██║╚██████╔╝██║     ███████╗███████║███████║╚██████╔╝██║  ██║
echo  ╚═╝     ╚═╝  ╚═╝ ╚═════╝ ╚═╝     ╚══════╝╚══════╝╚══════╝ ╚═════╝ ╚═╝  ╚═╝
echo.
echo                    🔥 MAXIMUM STEALTH PRIVACY PROTECTION 🔥
echo                         FOR PROFESSOR.DAVID SPECIAL USE
echo.
echo ================================================================================
echo.

REM Check for admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Administrator privileges detected - GOOD!
) else (
    echo ❌ ERROR: Administrator privileges required!
    echo.
    echo Please right-click this file and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo.
echo 🎯 QUICK SETUP OPTIONS:
echo.
echo 1. 🛡️  ACTIVATE PRIVACY SHIELD (Before using Augment)
echo 2. 🔥  RUN CLEANER V2 (After using Augment) 
echo 3. 📦  BUILD EXECUTABLES (First time setup)
echo 4. 📋  VIEW INSTRUCTIONS
echo 5. 🚪  EXIT
echo.
set /p choice="Select option (1-5): "

if "%choice%"=="1" goto shield
if "%choice%"=="2" goto cleaner
if "%choice%"=="3" goto build
if "%choice%"=="4" goto instructions
if "%choice%"=="5" goto exit
goto invalid

:shield
echo.
echo 🛡️ LAUNCHING PRIVACY SHIELD...
echo 🚨 This will provide REAL-TIME protection against data collection!
echo.
if exist "AugmentPrivacyTools\AugmentPrivacyShield.exe" (
    cd AugmentPrivacyTools
    AugmentPrivacyShield.exe
    cd ..
) else if exist "dist_shield\AugmentPrivacyShield.exe" (
    cd dist_shield
    AugmentPrivacyShield.exe
    cd ..
) else (
    echo ❌ Privacy Shield not found! Please build executables first (option 3)
    pause
)
goto menu

:cleaner
echo.
echo 🔥 LAUNCHING CLEANER V2...
echo 🚨 This will remove traces and deploy fake data!
echo.
if exist "AugmentPrivacyTools\AugmentCleanerV2.exe" (
    cd AugmentPrivacyTools
    AugmentCleanerV2.exe
    cd ..
) else if exist "dist_cleaner\AugmentCleanerV2.exe" (
    cd dist_cleaner
    AugmentCleanerV2.exe
    cd ..
) else (
    echo ❌ Cleaner V2 not found! Please build executables first (option 3)
    pause
)
goto menu

:build
echo.
echo 📦 BUILDING EXECUTABLES...
call build_exe.bat
goto menu

:instructions
echo.
echo 📋 USAGE INSTRUCTIONS:
echo ================================================================================
echo.
echo 🎯 MAXIMUM STEALTH WORKFLOW:
echo.
echo STEP 1 - BEFORE using Augment:
echo   1. Run option 1 (Privacy Shield)
echo   2. Activate protection (option 1 or 2)
echo   3. Shield monitors in real-time
echo   4. Use Augment safely - it sees fake data
echo.
echo STEP 2 - AFTER using Augment:
echo   1. Deactivate Privacy Shield (option 4)
echo   2. Run option 2 (Cleaner V2)
echo   3. Use SPOOF mode for maximum stealth
echo   4. Cleaner removes traces and deploys fake data
echo.
echo STEP 3 - Account Recreation:
echo   1. Both tools working = Safe to create new account
echo   2. Use code "professor.david" for recognition
echo   3. New account sees completely different user
echo   4. Maximum anonymity achieved!
echo.
echo 🚨 IMPORTANT: Both tools require Administrator privileges!
echo.
pause
goto menu

:invalid
echo.
echo ❌ Invalid option. Please try again.
echo.
pause

:menu
echo.
echo Press any key to return to menu...
pause >nul
cls
goto start

:exit
echo.
echo 👋 Stay safe and anonymous, Professor.David!
echo.
pause
exit /b 0

:start
goto :eof
