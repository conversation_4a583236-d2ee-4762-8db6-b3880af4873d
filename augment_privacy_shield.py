import os
import sys
import json
import random
import string
import winreg
import subprocess
from datetime import datetime
import sqlite3
import shutil

class AugmentPrivacyShield:
    """Privacy protection layer that feeds fake data to Augment Code"""
    
    def __init__(self):
        self.fake_data = self.generate_fake_system_data()
        self.original_env = {}
        self.protection_active = False
        
    def generate_fake_system_data(self, custom_username=None, custom_computername=None, custom_domain=None):
        """Generate convincing fake system information, optionally with custom username/computer/domain"""
        fake_names = [
            "DevUser", "CodeMaster", "TechGuru", "BuildBot", "TestUser", "SysAdmin", "Quantum", "Neo", "Matrix", "Ghost", "Phantom", "Spectre", "ZeroCool", "AcidBurn", "CrashOverride", "Trinity", "Morpheus", "Root", "Admin", "Operator", "Cortex", "Pixel", "Bitwise", "Hex", "<PERSON>ip<PERSON>", "Shadow", "Echo", "Nova", "Atlas", "Vortex", "Falcon", "<PERSON>", "Vega", "Phoenix", "Raven", "Blaze", "Comet", "Drift", "<PERSON>ulse", "Glitch", "Zenith", "Vertex", "Nimbus", "<PERSON>ether", "<PERSON>ble", "Onyx", "Slate", "Frost", "Blitz", "Forge", "<PERSON>e", "Delta", "Sigma", "Omega", "<PERSON>", "<PERSON>", "Gamma", "<PERSON>da", "<PERSON>ta", "Epsilon", "<PERSON>eta", "Iota", "<PERSON>", "<PERSON>", "Nu", "<PERSON>", "<PERSON>", "Rho", "Tau", "Upsilon", "Phi", "Chi", "Psi", "OmegaX", "ByteMe", "StackTrace", "Overflow", "NullPointer", "CyberFox", "Debugger", "NightOwl", "Bear", "Tiger", "Panther", "Jaguar", "Lynx", "Eagle", "Hawk", "Crow", "Owl", "Dragon", "Phoenix", "Griffin", "Hydra", "Pegasus", "Merlin", "Gandalf", "Dumbledore", "Sherlock", "Watson", "Holmes", "Bond", "Q", "M", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"
        ]
        fake_computers = [
            "DEV-MACHINE", "BUILD-SERVER", "TEST-PC", "CODE-STATION", "WORKSTATION-01", "LAPTOP-ELITE", "DESKTOP-PRO", "SERVER-EDGE", "NODE-ALPHA", "NODE-BETA", "NODE-GAMMA", "LAB-TERMINAL", "VIRTUAL-HOST", "CLOUD-INSTANCE", "EDGE-DEVICE", "CORE-SYSTEM", "MAINFRAME", "QUANTUM-BOX", "AI-ENGINE", "DATA-CRUNCHER", "RENDER-FARM", "SIM-UNIT", "SECURE-VAULT", "SANDBOX", "ANALYTICS-NODE", "ALPHA-01", "OMEGA-02", "PHOENIX-03", "TITAN-04", "ATLAS-05", "ORION-06", "ZENITH-07", "VECTOR-08", "DELTA-09", "SIGMA-10", "GAMMA-11", "BETA-12", "ALPHA-13", "FOX-14", "WOLF-15", "EAGLE-16", "RAVEN-17", "DRAGON-18", "GRYPHON-19", "PEGASUS-20", "MERLIN-21", "SHERLOCK-22", "BOND-23", "MATRIX-24", "NEO-25", "TRINITY-26", "MORPHEUS-27", "AGENTSMITH-28", "ROOT-29", "ADMIN-30"
        ]
        fake_domains = [
            "WORKGROUP", "DEV-DOMAIN", "TEST-LAB", "CORP-NET", "INTRANET", "ENTERPRISE", "LAB-NET", "CLOUD-DOMAIN", "SECURE-DOMAIN", "VIRTUAL-DOMAIN", "ALPHA-NET", "OMEGA-NET", "PHOENIX-NET", "TITAN-NET", "ATLAS-NET", "ORION-NET", "ZENITH-NET", "VECTOR-NET", "DELTA-NET", "SIGMA-NET", "GAMMA-NET", "BETA-NET", "ALPHA-NET"
        ]
        username = custom_username if custom_username else random.choice(fake_names)
        computername = custom_computername if custom_computername else random.choice(fake_computers)
        userdomain = custom_domain if custom_domain else random.choice(fake_domains)
        return {
            'username': username,
            'computername': computername,
            'userdomain': userdomain,
            'userprofile': f"C:\\Users\\<USER>\FakeAugmentShield\ProcessorInfo"
            
            with winreg.CreateKey(winreg.HKEY_CURRENT_USER, fake_reg_path) as key:
                winreg.SetValueEx(key, "ProcessorNameString", 0, winreg.REG_SZ, 
                                f"{self.fake_data['processor']['brand']} {self.fake_data['processor']['model']}")
                winreg.SetValueEx(key, "~MHz", 0, winreg.REG_DWORD, 
                                int(float(self.fake_data['processor']['speed'].split()[0]) * 1000))
            
            print("   🗂️ Created fake registry entries")
        except Exception as e:
            print(f"   ❌ Registry patching failed: {str(e)}")
    
    def setup_wmi_interception(self):
        """Setup WMI query interception (advanced technique)"""
        # This would require more advanced hooking techniques
        # For now, we'll focus on environment and file-based interception
        print("   🔧 WMI interception setup (placeholder)")
    
    def setup_filesystem_interception(self):
        """Setup file system query interception"""
        # Create fake system info files in temp directory
        temp_dir = os.path.join(os.environ.get('TEMP', ''), 'AugmentShield')
        os.makedirs(temp_dir, exist_ok=True)
        
        # Create fake system info file
        fake_sysinfo = {
            'computer_name': self.fake_data['computername'],
            'username': self.fake_data['username'],
            'processor': self.fake_data['processor'],
            'memory': self.fake_data['memory'],
            'network': self.fake_data['network']
        }
        
        with open(os.path.join(temp_dir, 'system_info.json'), 'w') as f:
            json.dump(fake_sysinfo, f, indent=2)
        
        print("   📁 Created fake system info files")
    
    def setup_data_interception(self):
        """Setup interception of Augment's data collection attempts"""
        # Monitor VSCode state database for Augment writes
        self.monitor_vscode_databases()
        
        # Intercept network requests (placeholder)
        self.setup_network_interception()
    
    def monitor_vscode_databases(self):
        """Monitor and modify VSCode databases to inject fake data"""
        vscode_paths = [
            os.path.expandvars(r"%APPDATA%\Code\User\globalStorage"),
            os.path.expandvars(r"%APPDATA%\Code - Insiders\User\globalStorage"),
            os.path.expandvars(r"%APPDATA%\Cursor\User\globalStorage")
        ]
        
        for vscode_path in vscode_paths:
            state_db = os.path.join(vscode_path, "state.vscdb")
            if os.path.exists(state_db):
                self.inject_fake_data_to_database(state_db)
    
    def inject_fake_data_to_database(self, db_path):
        """Inject fake data into VSCode state database"""
        try:
            # Backup original database
            backup_path = db_path + ".shield_backup"
            shutil.copy2(db_path, backup_path)
            
            conn = sqlite3.connect(db_path)
            cur = conn.cursor()
            
            # Inject fake system information
            fake_entries = [
                ('augment.system.username', self.fake_data['username']),
                ('augment.system.computername', self.fake_data['computername']),
                ('augment.system.processor', json.dumps(self.fake_data['processor'])),
                ('augment.system.memory', json.dumps(self.fake_data['memory'])),
                ('augment.system.network', json.dumps(self.fake_data['network'])),
                ('augment.telemetry.hardware', json.dumps({
                    'cpu': self.fake_data['processor'],
                    'memory': self.fake_data['memory'],
                    'gpu': self.fake_data['gpu']
                }))
            ]
            
            for key, value in fake_entries:
                # Insert or update fake data
                cur.execute("INSERT OR REPLACE INTO ItemTable (key, value) VALUES (?, ?)", 
                          (key, value))
            
            conn.commit()
            conn.close()
            
            print(f"   💉 Injected fake data into {os.path.basename(db_path)}")
            
        except Exception as e:
            print(f"   ❌ Database injection failed: {str(e)}")
    
    def setup_network_interception(self):
        """Setup network request interception (placeholder)"""
        # This would require more advanced techniques like proxy or DLL injection
        print("   🌐 Network interception setup (placeholder)")
    
    def deactivate_protection(self):
        """Deactivate privacy protection and restore original settings"""
        if not self.protection_active:
            print("⚠️ Privacy Shield is not active")
            return
        
        print("🔄 Deactivating Augment Privacy Shield...")
        
        # Restore original environment variables
        for var, value in self.original_env.items():
            os.environ[var] = value
        
        # Clean up fake registry entries
        self.cleanup_fake_registry()
        
        # Clean up fake files
        self.cleanup_fake_files()
        
        # Restore database backups
        self.restore_database_backups()
        
        self.protection_active = False
        print("✅ Privacy Shield deactivated. Original system data restored.")
    
    def cleanup_fake_registry(self):
        """Clean up fake registry entries"""
        try:
            winreg.DeleteKey(winreg.HKEY_CURRENT_USER, r"SOFTWARE\FakeAugmentShield\ProcessorInfo")
            winreg.DeleteKey(winreg.HKEY_CURRENT_USER, r"SOFTWARE\FakeAugmentShield")
            print("   🗂️ Cleaned up fake registry entries")
        except Exception as e:
            print(f"   ❌ Registry cleanup failed: {str(e)}")
    
    def cleanup_fake_files(self):
        """Clean up fake system info files"""
        temp_dir = os.path.join(os.environ.get('TEMP', ''), 'AugmentShield')
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
            print("   📁 Cleaned up fake files")
    
    def restore_database_backups(self):
        """Restore original database backups"""
        vscode_paths = [
            os.path.expandvars(r"%APPDATA%\Code\User\globalStorage"),
            os.path.expandvars(r"%APPDATA%\Code - Insiders\User\globalStorage"),
            os.path.expandvars(r"%APPDATA%\Cursor\User\globalStorage")
        ]
        
        for vscode_path in vscode_paths:
            state_db = os.path.join(vscode_path, "state.vscdb")
            backup_db = state_db + ".shield_backup"
            
            if os.path.exists(backup_db):
                shutil.copy2(backup_db, state_db)
                os.remove(backup_db)
                print(f"   💾 Restored {os.path.basename(state_db)}")
    
    def status_report(self):
        """Show current protection status"""
        print("\n" + "=" * 50)
        print("🛡️ AUGMENT PRIVACY SHIELD STATUS")
        print("=" * 50)
        print(f"Protection Active: {'✅ YES' if self.protection_active else '❌ NO'}")
        
        if self.protection_active:
            print(f"Fake Username: {self.fake_data['username']}")
            print(f"Fake Computer: {self.fake_data['computername']}")
            print(f"Fake CPU: {self.fake_data['processor']['brand']} {self.fake_data['processor']['model']}")
            print(f"Fake Memory: {self.fake_data['memory']['total'] // (1024**3)} GB")
            print(f"Fake IP: {self.fake_data['network']['ip']}")

def main():
    """Main function for privacy shield control"""
    shield = AugmentPrivacyShield()
    print("🛡️ Augment Privacy Shield - Fake Data Injection Tool")
    print("=" * 60)
    print("This tool feeds fake system information to Augment Code")
    print("to protect your real personal and system data.")
    print()
    while True:
        print("\nOptions:")
        print("1. 🛡️ Activate Privacy Shield (random fake user)")
        print("2. 🛡️ Activate Privacy Shield (custom username/computer/domain)")
        print("3. � Switch Fake User (choose another random or custom user)")
        print("4. �🔄 Deactivate Privacy Shield")
        print("5. 📊 Show Status")
        print("6. 🚪 Exit")
        choice = input("\nSelect option (1-6): ").strip()
        if choice == '1':
            shield.fake_data = shield.generate_fake_system_data()
            shield.activate_protection()
        elif choice == '2':
            custom_name = input("Enter custom username: ").strip()
            if not custom_name:
                print("❌ Username cannot be empty.")
                continue
            custom_computer = input("Enter custom computer name (or leave blank for random): ").strip()
            custom_domain = input("Enter custom domain (or leave blank for random): ").strip()
            shield.fake_data = shield.generate_fake_system_data(
                custom_username=custom_name,
                custom_computername=custom_computer if custom_computer else None,
                custom_domain=custom_domain if custom_domain else None
            )
            shield.activate_protection()
        elif choice == '3':
            print("\nSwitch Fake User:")
            print("  1. Choose another random fake user")
            print("  2. Enter a new custom username/computer/domain")
            sub_choice = input("Select option (1-2): ").strip()
            if sub_choice == '1':
                shield.fake_data = shield.generate_fake_system_data()
                print("✅ Switched to a new random fake user.")
            elif sub_choice == '2':
                custom_name = input("Enter custom username: ").strip()
                if not custom_name:
                    print("❌ Username cannot be empty.")
                    continue
                custom_computer = input("Enter custom computer name (or leave blank for random): ").strip()
                custom_domain = input("Enter custom domain (or leave blank for random): ").strip()
                shield.fake_data = shield.generate_fake_system_data(
                    custom_username=custom_name,
                    custom_computername=custom_computer if custom_computer else None,
                    custom_domain=custom_domain if custom_domain else None
                )
                print("✅ Switched to new custom fake user.")
            else:
                print("❌ Invalid option.")
            if shield.protection_active:
                shield.activate_protection()
        elif choice == '4':
            shield.deactivate_protection()
        elif choice == '5':
            shield.status_report()
        elif choice == '6':
            if shield.protection_active:
                print("⚠️ Deactivating protection before exit...")
                shield.deactivate_protection()
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid option. Please try again.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ Interrupted by user. Cleaning up...")
        # Emergency cleanup if needed
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
        print("Press Enter to exit...")
        input()
