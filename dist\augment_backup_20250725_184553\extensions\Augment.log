2025-07-25 16:55:02.225 [info] 'AugmentConfigListener' settings parsed successfully
2025-07-25 16:55:02.225 [info] 'AugmentConfigListener' Config changed from <unset> to {"apiToken":"","completionURL":"","modelName":"","conflictingCodingAssistantCheck":true,"codeInstruction":{},"chat":{"enableEditableHistory":false,"useRichTextHistory":true,"smartPasteUsePrecomputation":true,"experimentalFullFilePaste":false,"modelDisplayNameToId":{"Augment":null},"userGuidelines":""},"agent":{},"oauth":{"clientID":"augment-vscode-extension","url":"https://auth.augmentcode.com"},"enableUpload":true,"shortcutsDisplayDelayMS":2000,"enableEmptyFileHint":true,"disableFocusOnAugmentPanel":false,"enableDataCollection":false,"enableDebugFeatures":false,"enableReviewerWorkflows":false,"completions":{"enableAutomaticCompletions":true,"disableCompletionsByLanguage":{},"enableQuickSuggestions":true,"timeoutMs":800,"maxWaitMs":1600,"addIntelliSenseSuggestions":true},"openFileManager":{},"nextEdit":{"backgroundEnabled":true,"useCursorDecorations":false,"allowDuringDebugging":false,"useMockResults":false,"noDiffModeUseCodeLens":false,"enableBackgroundSuggestions":true,"enableGlobalBackgroundSuggestions":false,"highlightSuggestionsInTheEditor":false,"showDiffInHover":false,"enableAutoApply":true},"recencySignalManager":{"collectTabSwitchEvents":false},"preferenceCollection":{"enable":false,"enableRetrievalDataCollection":false,"enableRandomizedMode":true},"vcs":{"watcherEnabled":false},"git":{},"smartPaste":{},"instructions":{},"integrations":{},"mcpServers":[],"advanced":{}}
2025-07-25 16:55:02.226 [info] 'FeatureFlagManager' feature flags changed from <unset> to {"gitDiff":false,"gitDiffPollingFrequencyMSec":0,"additionalChatModels":"","smallSyncThreshold":15,"bigSyncThreshold":1000,"enableWorkspaceManagerUi":true,"enableInstructions":false,"enableSmartPaste":false,"enableSmartPasteMinVersion":"","enablePromptEnhancer":false,"enableViewTextDocument":false,"bypassLanguageFilter":false,"enableHindsight":false,"maxUploadSizeBytes":131072,"vscodeNextEditBottomPanelMinVersion":"","vscodeNextEditMinVersion":"","vscodeNextEditUx1MaxVersion":"","vscodeNextEditUx2MaxVersion":"","vscodeFlywheelMinVersion":"","vscodeExternalSourcesInChatMinVersion":"","vscodeShareMinVersion":"","maxTrackableFileCount":250000,"maxTrackableFileCountWithoutPermission":150000,"minUploadedPercentageWithoutPermission":90,"memoryClassificationOnFirstToken":false,"vscodeSourcesMinVersion":"","vscodeChatHintDecorationMinVersion":"","nextEditDebounceMs":400,"enableCompletionFileEditEvents":false,"vscodeEnableCpuProfile":false,"verifyFolderIsSourceRepo":false,"refuseToSyncHomeDirectories":false,"enableFileLimitsForSyncingPermission":false,"enableChatMermaidDiagrams":false,"enableSummaryTitles":false,"smartPastePrecomputeMode":"visible-hover","vscodeNewThreadsMenuMinVersion":"","enableNewThreadsList":false,"enableUntruncatedContentStorage":false,"maxLinesTerminalProcessOutput":0,"vscodeEditableHistoryMinVersion":"","vscodeEnableChatMermaidDiagramsMinVersion":"","userGuidelinesLengthLimit":2000,"workspaceGuidelinesLengthLimit":2000,"useCheckpointManagerContextMinVersion":"","validateCheckpointManagerContext":false,"vscodeDesignSystemRichTextEditorMinVersion":"","allowClientFeatureFlagOverrides":false,"vscodeChatWithToolsMinVersion":"","vscodeChatMultimodalMinVersion":"","vscodeAgentModeMinVersion":"","vscodeAgentModeMinStableVersion":"","vscodeBackgroundAgentsMinVersion":"","vscodeAgentEditTool":"backend_edit_tool","vscodeRichCheckpointInfoMinVersion":"","vscodeDirectApplyMinVersion":"","vscodeMinVersion":"1.96.0","memoriesParams":{},"eloModelConfiguration":{"highPriorityModels":[],"regularBattleModels":[],"highPriorityThreshold":0.5},"vscodeChatStablePrefixTruncationMinVersion":"","agentEditToolMinViewSize":0,"agentEditToolSchemaType":"StrReplaceEditorToolDefinitionNested","agentEditToolEnableFuzzyMatching":false,"agentEditToolFuzzyMatchSuccessMessage":"Replacement successful. old_str and new_str were slightly modified to match the original file content.","agentEditToolFuzzyMatchMaxDiff":50,"agentEditToolFuzzyMatchMaxDiffRatio":0.15,"agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs":5,"agentEditToolInstructionsReminder":false,"agentEditToolShowResultSnippet":true,"agentEditToolMaxLines":200,"agentSaveFileToolInstructionsReminder":false,"vscodePersonalitiesMinVersion":"","useMemorySnapshotManager":false,"vscodeGenerateCommitMessageMinVersion":"","enableRules":false,"memoriesTextEditorEnabled":false,"enableModelRegistry":false,"openFileManagerV2Enabled":false,"modelRegistry":{},"vscodeTaskListMinVersion":"","vscodeSupportToolUseStartMinVersion":"","enableAgentAutoMode":false,"enableAgentSwarmMode":false,"vscodeRemoteAgentSSHMinVersion":"","clientAnnouncement":"","grepSearchToolEnable":false,"grepSearchToolTimelimitSec":10,"grepSearchToolOutputCharsLimit":5000,"grepSearchToolNumContextLines":5,"historySummaryMinVersion":"","historySummaryParams":"","enableCommitIndexing":false,"maxCommitsToIndex":0,"enableExchangeStorage":false,"conversationHistorySizeThresholdBytes":268435456,"enableToolUseStateStorage":false,"retryChatStreamTimeouts":false,"remoteAgentCurrentWorkspace":false,"enableMemoryRetrieval":false,"enableAgentTabs":false,"enableSwarmMode":false,"enableGroupedTools":false,"remoteAgentsResumeHintAvailableTtlDays":0,"enableParallelTools":false,"enableAgentGitTracker":false}
2025-07-25 16:55:02.226 [info] 'SidecarAnalytics' Segment analytics initialized for vscode
2025-07-25 16:55:02.226 [info] 'AugmentConfigListener' settings parsed successfully
2025-07-25 16:55:02.226 [info] 'AugmentExtension' Retrieving model config
2025-07-25 16:55:10.968 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 8286 msec late.
2025-07-25 16:55:11.633 [info] 'AugmentExtension' Retrieved model config
2025-07-25 16:55:11.633 [info] 'AugmentExtension' Returning model config
2025-07-25 16:55:11.885 [info] 'FeatureFlagManager' feature flags changed:
  - additionalChatModels: "" to "{}"
  - enableInstructions: false to true
  - enableSmartPasteMinVersion: "" to "0.267.0"
  - enablePromptEnhancer: false to true
  - enableViewTextDocument: false to true
  - bypassLanguageFilter: false to true
  - maxUploadSizeBytes: 131072 to 524288
  - vscodeNextEditBottomPanelMinVersion: "" to "0.394.0"
  - vscodeNextEditMinVersion: "" to "0.343.0"
  - vscodeFlywheelMinVersion: "" to "0.282.0"
  - vscodeExternalSourcesInChatMinVersion: "" to "0.243.2"
  - memoryClassificationOnFirstToken: false to true
  - vscodeChatHintDecorationMinVersion: "" to "0.274.0"
  - enableCompletionFileEditEvents: false to true
  - verifyFolderIsSourceRepo: false to true
  - refuseToSyncHomeDirectories: false to true
  - enableFileLimitsForSyncingPermission: false to true
  - enableSummaryTitles: false to true
  - smartPastePrecomputeMode: "visible-hover" to "visible"
  - vscodeNewThreadsMenuMinVersion: "" to "0.305.0"
  - enableNewThreadsList: false to true
  - vscodeEditableHistoryMinVersion: "" to "0.330.0"
  - vscodeEnableChatMermaidDiagramsMinVersion: "" to "0.314.0"
  - userGuidelinesLengthLimit: 2000 to 24576
  - workspaceGuidelinesLengthLimit: 2000 to 49512
  - useCheckpointManagerContextMinVersion: "" to "0.323.0"
  - vscodeDesignSystemRichTextEditorMinVersion: "" to "0.363.0"
  - vscodeChatMultimodalMinVersion: "" to "0.384.0"
  - vscodeAgentModeMinVersion: "" to "0.395.0"
  - vscodeAgentModeMinStableVersion: "" to "0.399.1"
  - vscodeBackgroundAgentsMinVersion: "" to "0.472.1"
  - vscodeAgentEditTool: "backend_edit_tool" to "str_replace_editor_tool"
  - vscodeDirectApplyMinVersion: "" to "0.499.0"
  - eloModelConfiguration > highPriorityModels: [] to undefined
  - eloModelConfiguration > regularBattleModels: [] to undefined
  - eloModelConfiguration > highPriorityThreshold: 0.5 to undefined
  - vscodeChatStablePrefixTruncationMinVersion: "" to "0.402.0"
  - agentEditToolSchemaType: "StrReplaceEditorToolDefinitionNested" to "StrReplaceEditorToolDefinitionFlat"
  - agentEditToolEnableFuzzyMatching: false to true
  - agentEditToolInstructionsReminder: false to true
  - agentEditToolShowResultSnippet: true to false
  - agentEditToolMaxLines: 200 to 150
  - agentSaveFileToolInstructionsReminder: false to true
  - useMemorySnapshotManager: false to true
  - enableRules: false to true
  - memoriesTextEditorEnabled: false to true
  - vscodeTaskListMinVersion: "" to "0.482.0"
  - vscodeSupportToolUseStartMinVersion: "" to "0.485.0"
  - enableAgentAutoMode: false to true
  - vscodeRemoteAgentSSHMinVersion: "" to "0.456.0"
  - historySummaryParams: "" to "{\"buffer_time_before_cache_expiration_ms\": 30000, \"cache_ttl_ms\": 300000, \"history_tail_size_chars_to_exclude\": 80000, \"prompt\": \"Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.\\nThis summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing with the conversation and supporting any continuing tasks.\\n\\nYour summary should be structured as follows:\\nContext: The context to continue the conversation with. If applicable based on the current task, this should include:\\n1. Previous Conversation: High level details about what was discussed throughout the entire conversation with the user. This should be written to allow someone to be able to follow the general overarching conversation flow.\\n2. Current Work: Describe in detail what was being worked on prior to this request to summarize the conversation. Pay special attention to the more recent messages in the conversation.\\n3. Key Technical Concepts: List all important technical concepts, technologies, coding conventions, and frameworks discussed, which might be relevant for continuing with this work.\\n4. Relevant Files and Code: If applicable, enumerate specific files and code sections examined, modified, or created for the task continuation. Pay special attention to the most recent messages and changes.\\n5. Problem Solving: Document problems solved thus far and any ongoing troubleshooting efforts.\\n6. Pending Tasks and Next Steps: Outline all pending tasks that you have explicitly been asked to work on, as well as list the next steps you will take for all outstanding work, if applicable. Include code snippets where they add clarity. For any next steps, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no information loss in context between tasks.\\n\\nExample summary structure:\\n1. Previous Conversation:\\n[Detailed description]\\n2. Current Work:\\n[Detailed description]\\n3. Key Technical Concepts:\\n- [Concept 1]\\n- [Concept 2]\\n- [...]\\n4. Relevant Files and Code:\\n- [File Name 1]\\n    - [Summary of why this file is important]\\n    - [Summary of the changes made to this file, if any]\\n    - [Important Code Snippet]\\n- [File Name 2]\\n    - [Important Code Snippet]\\n- [...]\\n5. Problem Solving:\\n[Detailed description]\\n6. Pending Tasks and Next Steps:\\n- [Task 1 details & next steps]\\n- [Task 2 details & next steps]\\n- [...]\\n\\nOutput only the summary of the conversation so far, without any additional commentary or explanation.\\n\", \"trigger_on_history_size_chars\": 200000, \"trigger_on_history_size_chars_when_cache_expiring\": 140000}"
  - enableCommitIndexing: false to true
  - maxCommitsToIndex: 0 to 10000
  - retryChatStreamTimeouts: false to true
  - remoteAgentsResumeHintAvailableTtlDays: 0 to 21
2025-07-25 16:55:11.886 [info] 'SyncingPermissionTracker' Initial syncing permission: syncing permission granted for workspace. Folders:
    c:\Users\<USER>\Desktop\my_app\react (explicit) at 7/25/2025, 4:47:02 PM
2025-07-25 16:55:11.886 [info] 'WorkspaceManager' OpenFileManagerProxy created. V2 enabled: [false]
2025-07-25 16:55:11.886 [info] 'BlobsCheckpointManager' BlobsCheckpointManager created. checkpointThreshold: 1000
2025-07-25 16:55:11.886 [info] 'SyncingPermissionTracker' Permission to sync folder c:\Users\<USER>\Desktop\my_app\react granted at 7/25/2025, 4:47:02 PM; type = explicit
2025-07-25 16:55:11.886 [info] 'WorkspaceManager' Adding workspace folder react; folderRoot = c:\Users\<USER>\Desktop\my_app\react; syncingPermission = granted
2025-07-25 16:55:11.886 [info] 'SyncingPermissionTracker' Updating syncing permission: syncing permission granted for workspace. Folders:
    c:\Users\<USER>\Desktop\my_app\react (explicit) at 7/25/2025, 4:47:02 PM
2025-07-25 16:55:12.153 [info] 'AugmentExtension' Git tracking disabled by enableAgentGitTracker feature flag
2025-07-25 16:55:12.153 [info] 'MemoryWebviewMessageHandler' Memory webview message handler initialized
2025-07-25 16:55:12.153 [info] 'RemoteAgentsMessenger' RemoteAgentsMessenger initialized, setting up onDidChangeTextDocument listener
2025-07-25 16:55:12.153 [info] 'RemoteAgentsMessenger' Registering RemoteAgentsMessenger with AsyncMsgHandler
2025-07-25 16:55:12.153 [info] 'HotKeyHints' HotKeyHints initialized
2025-07-25 16:55:12.176 [info] 'AugmentExtension' Git tracking disabled by enableAgentGitTracker feature flag
2025-07-25 16:55:12.178 [info] 'ToolsModel' Loaded saved chat mode: CHAT
2025-07-25 16:55:12.203 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-07-25 16:55:14.240 [info] 'RemoteAgentsMessenger' Remote agent status handler called
2025-07-25 16:55:14.240 [info] 'RemoteAgentsMessenger' Remote agent status: isRemoteAgentSshWindow=false, remoteAgentId=undefined
2025-07-25 16:55:14.247 [info] 'TaskManager' Setting current root task UUID to 30e6ac19-cf95-46f7-ba46-7ca036adb814
2025-07-25 16:55:15.243 [info] 'DynamicLevelKvStore' Ensuring LevelDB is initialized
2025-07-25 16:55:15.247 [info] 'WorkspaceManager[react]' Start tracking
2025-07-25 16:55:17.556 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 2959 msec late.
2025-07-25 16:55:17.675 [info] 'StallDetector' Recent work: [{"name":"get-remote-agent-notification-enabled-request","durationMs":3351.2248,"timestamp":"2025-07-25T15:55:17.582Z"},{"name":"get-remote-agent-notification-enabled-request","durationMs":3099.0645,"timestamp":"2025-07-25T15:55:17.662Z"},{"name":"get-remote-agent-notification-enabled-request","durationMs":3091.5448,"timestamp":"2025-07-25T15:55:17.663Z"}]
2025-07-25 16:55:28.302 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 10526 msec late.
2025-07-25 16:55:28.302 [info] 'StallDetector' Recent work: [{"name":"get-remote-agent-pinned-status-request","durationMs":13726.4485,"timestamp":"2025-07-25T15:55:27.961Z"}]
2025-07-25 16:55:28.491 [error] 'AugmentExtensionSidecar' API request f7012faf-7343-466e-89ee-ec4f30a25289 to https://d1.api.augmentcode.com/remote-agents/list-stream failed: This operation was aborted
2025-07-25 16:55:28.491 [error] 'AugmentExtensionSidecar' AbortError: This operation was aborted
    at node:internal/deps/undici/undici:13510:13
2025-07-25 16:55:29.628 [error] 'AugmentExtension' API request 924b2ec0-c983-406f-96f4-101928944191 to https://d1.api.augmentcode.com/agents/list-remote-tools failed: fetch failed (due to {"errno":-4077,"code":"ECONNRESET","syscall":"read"})
2025-07-25 16:55:29.631 [error] 'AugmentExtension' API request 36c7fe43-1a3f-400e-b1ae-69b43f400678 to https://d1.api.augmentcode.com/subscription-info failed: fetch failed (due to {"errno":-4077,"code":"ECONNRESET","syscall":"read"})
2025-07-25 16:55:29.671 [error] 'GitReferenceMessenger' Failed to locally get remote url: Failed to get remote url, no remote found
2025-07-25 16:55:29.673 [info] 'StallDetector' Recent work: [{"name":"get-remote-url-request","durationMs":14462.3891,"timestamp":"2025-07-25T15:55:29.668Z"}]
2025-07-25 16:55:31.087 [error] 'AugmentExtension' API request 0abe8809-e152-4545-9840-35e4f22b72a6 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 16:55:31.087 [error] 'AugmentExtension' Dropping error report "remote-agents/list-stream call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-25 16:55:31.114 [error] 'AugmentExtension' API request 9b9f7396-bb0e-4bb3-9a3c-187a53223556 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 16:55:31.114 [error] 'AugmentExtension' Dropping error report "agents/list-remote-tools call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-25 16:55:31.115 [error] 'VSCodeRemoteInfo' Failed to list remote tools fetch failed
2025-07-25 16:55:31.117 [error] 'AugmentExtension' API request 9ed71605-270d-40e2-ba77-34ec76a860be to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 16:55:31.118 [error] 'AugmentExtension' Dropping error report "subscription-info call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-25 16:55:31.118 [error] 'ChatApp' Failed to get subscription info: Error: fetch failed
2025-07-25 16:55:31.120 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":16867.7735,"timestamp":"2025-07-25T15:55:31.115Z"}]
2025-07-25 16:55:31.586 [info] 'PathMap' Opened source folder c:\Users\<USER>\Desktop\my_app\react with id 100
2025-07-25 16:55:31.586 [info] 'OpenFileManager' Opened source folder 100
2025-07-25 16:55:31.597 [info] 'MtimeCache[react]' reading blob name cache from c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\d33968898213501e1fa09ef1a69642e5\Augment.vscode-augment\e925d4ab58322ab19350cb58144515b303291a91b3ebfb54b2fd8bcccab30582\mtime-cache.json
2025-07-25 16:55:31.598 [info] 'MtimeCache[react]' no blob name cache found at c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\d33968898213501e1fa09ef1a69642e5\Augment.vscode-augment\e925d4ab58322ab19350cb58144515b303291a91b3ebfb54b2fd8bcccab30582\mtime-cache.json (probably new source folder); error = ENOENT: no such file or directory, open 'c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\d33968898213501e1fa09ef1a69642e5\Augment.vscode-augment\e925d4ab58322ab19350cb58144515b303291a91b3ebfb54b2fd8bcccab30582\mtime-cache.json'
2025-07-25 16:55:31.636 [info] 'AugmentExtension' Git tracking disabled by enableAgentGitTracker feature flag
2025-07-25 16:55:31.643 [info] 'WorkspaceManager[react]' Tracking enabled
2025-07-25 16:55:31.643 [info] 'WorkspaceManager[react]' Path metrics:
  - directories emitted: 0
  - files emitted: 0
  - other paths emitted: 0
  - total paths emitted: 0
  - timing stats:
    - readDir: 0 ms
    - filter: 0 ms
    - yield: 0 ms
    - total: 1 ms
2025-07-25 16:55:31.643 [info] 'WorkspaceManager[react]' File metrics:
  - paths accepted: 0
  - paths not accessible: 0
  - not plain files: 0
  - large files: 0
  - blob name calculation fails: 0
  - encoding errors: 0
  - mtime cache hits: 0
  - mtime cache misses: 0
  - probe batches: 0
  - blob names probed: 0
  - files read: 0
  - blobs uploaded: 0
  - timing stats:
    - ingestPath: 0 ms
    - probe: 0 ms
    - stat: 0 ms
    - read: 0 ms
    - upload: 0 ms
2025-07-25 16:55:31.643 [info] 'WorkspaceManager[react]' Startup metrics:
  - create SourceFolder: 16351 ms
  - read MtimeCache: 1 ms
  - pre-populate PathMap: 0 ms
  - create PathFilter: 23 ms
  - create PathNotifier: 1 ms
  - enumerate paths: 9 ms
  - purge stale PathMap entries: 0 ms
  - enumerate: 0 ms
  - await DiskFileManager quiesced: 5 ms
  - enable persist: 1 ms
  - total: 16391 ms
2025-07-25 16:55:31.644 [info] 'WorkspaceManager' Workspace startup complete in 19829 ms
2025-07-25 16:55:31.846 [info] 'ToolsModel' Tools Mode: CHAT (0 hosts)
2025-07-25 16:55:47.745 [info] 'ToolsModel' Saved chat mode: AGENT
2025-07-25 16:55:47.815 [info] 'ToolsModel' Tools Mode: AGENT (3 hosts)
2025-07-25 16:55:47.816 [info] 'ToolsModel' Host: localToolHost (10 tools: 178 enabled, 0 disabled})
 + str-replace-editor
 + open-browser
 + diagnostics
 + read-terminal
 + git-commit-retrieval
 + launch-process
 + kill-process
 + read-process
 + write-process
 + list-processes

2025-07-25 16:55:49.162 [info] 'ToolsModel' Host: remoteToolHost (1 tools: 13 enabled, 0 disabled})
 + web-search

2025-07-25 16:55:49.162 [info] 'ToolsModel' Host: sidecarToolHost (13 tools: 218 enabled, 0 disabled})
 + web-fetch
 + codebase-retrieval
 + remove-files
 + save-file
 + view_tasklist
 + reorganize_tasklist
 + update_tasks
 + add_tasks
 + remember
 + render-mermaid
 + view-range-untruncated
 + search-untruncated
 + view

2025-07-25 16:56:16.172 [info] 'ViewTool' Tool called with path: . and view_range: undefined
2025-07-25 16:56:16.175 [info] 'ViewTool' Listing directory: . (depth: 2, showHidden: false)
2025-07-25 16:57:43.192 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1173.8897,"timestamp":"2025-07-25T15:57:43.190Z"}]
2025-07-25 16:58:57.924 [error] 'AugmentExtension' API request ec26ba61-9598-4f75-bf41-a273e6203755 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 16:58:57.925 [error] 'AugmentExtension' Dropping error report "Request timed out: chat-user-message, id: 4c969d96-8e53-4a34-81c7-560088deca3e" due to error: This operation was aborted
2025-07-25 17:00:51.896 [info] 'WorkspaceManager[react]' Directory created: public
2025-07-25 17:00:51.938 [info] 'WorkspaceManager[react]' Directory created: src
2025-07-25 17:00:51.941 [info] 'WorkspaceManager[react]' Directory created: src\assets
2025-07-25 17:02:12.146 [info] 'WorkspaceManager[react]' Directory created: node_modules
2025-07-25 17:02:12.597 [info] 'WorkspaceManager[react]' Directory created: node_modules\@ampproject
2025-07-25 17:02:12.848 [info] 'WorkspaceManager[react]' Directory created: node_modules\@ampproject\remapping
2025-07-25 17:02:12.849 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel
2025-07-25 17:02:12.860 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\code-frame
2025-07-25 17:02:12.860 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\compat-data
2025-07-25 17:02:12.861 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\core
2025-07-25 17:02:12.861 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\generator
2025-07-25 17:02:12.862 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\helper-compilation-targets
2025-07-25 17:02:12.862 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\helper-globals
2025-07-25 17:02:12.863 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\helper-module-imports
2025-07-25 17:02:12.864 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\helper-module-transforms
2025-07-25 17:02:12.865 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\helper-plugin-utils
2025-07-25 17:02:12.866 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\helper-string-parser
2025-07-25 17:02:12.867 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\helper-validator-identifier
2025-07-25 17:02:12.868 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\helper-validator-option
2025-07-25 17:02:12.869 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\helpers
2025-07-25 17:02:12.869 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\parser
2025-07-25 17:02:12.870 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\plugin-transform-react-jsx-self
2025-07-25 17:02:12.871 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\plugin-transform-react-jsx-source
2025-07-25 17:02:12.871 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\template
2025-07-25 17:02:12.872 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\traverse
2025-07-25 17:02:12.873 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\types
2025-07-25 17:02:12.874 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild
2025-07-25 17:02:12.885 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\aix-ppc64
2025-07-25 17:02:12.885 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\android-arm
2025-07-25 17:02:12.886 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint
2025-07-25 17:02:12.890 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint-community
2025-07-25 17:02:12.892 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint-community\regexpp
2025-07-25 17:02:12.893 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\config-array
2025-07-25 17:02:12.893 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\config-helpers
2025-07-25 17:02:12.894 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\core
2025-07-25 17:02:12.896 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\object-schema
2025-07-25 17:02:12.897 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\plugin-kit
2025-07-25 17:02:12.898 [info] 'WorkspaceManager[react]' Directory created: node_modules\@humanfs
2025-07-25 17:02:12.901 [info] 'WorkspaceManager[react]' Directory created: node_modules\@humanfs\core
2025-07-25 17:02:12.903 [info] 'WorkspaceManager[react]' Directory created: node_modules\@humanwhocodes
2025-07-25 17:02:12.905 [info] 'WorkspaceManager[react]' Directory created: node_modules\@humanwhocodes\retry
2025-07-25 17:02:12.907 [info] 'WorkspaceManager[react]' Directory created: node_modules\@jridgewell
2025-07-25 17:02:12.909 [info] 'WorkspaceManager[react]' Directory created: node_modules\@jridgewell\resolve-uri
2025-07-25 17:02:12.909 [info] 'WorkspaceManager[react]' Directory created: node_modules\@jridgewell\sourcemap-codec
2025-07-25 17:02:12.910 [info] 'WorkspaceManager[react]' Directory created: node_modules\@jridgewell\trace-mapping
2025-07-25 17:02:12.911 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rolldown
2025-07-25 17:02:12.912 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rolldown\pluginutils
2025-07-25 17:02:12.913 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup
2025-07-25 17:02:12.919 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-android-arm-eabi
2025-07-25 17:02:12.920 [info] 'WorkspaceManager[react]' Directory created: node_modules\@types
2025-07-25 17:02:12.925 [info] 'WorkspaceManager[react]' Directory created: node_modules\@types\babel__core
2025-07-25 17:02:12.925 [info] 'WorkspaceManager[react]' Directory created: node_modules\@types\babel__generator
2025-07-25 17:02:12.930 [info] 'WorkspaceManager[react]' Directory created: node_modules\@types\babel__template
2025-07-25 17:02:12.932 [info] 'WorkspaceManager[react]' Directory created: node_modules\@types\babel__traverse
2025-07-25 17:02:12.933 [info] 'WorkspaceManager[react]' Directory created: node_modules\@types\estree
2025-07-25 17:02:12.933 [info] 'WorkspaceManager[react]' Directory created: node_modules\@types\json-schema
2025-07-25 17:02:12.934 [info] 'WorkspaceManager[react]' Directory created: node_modules\@vitejs
2025-07-25 17:02:12.935 [info] 'WorkspaceManager[react]' Directory created: node_modules\@vitejs\plugin-react
2025-07-25 17:02:12.936 [info] 'WorkspaceManager[react]' Directory created: node_modules\ajv
2025-07-25 17:02:12.937 [info] 'WorkspaceManager[react]' Directory created: node_modules\ansi-styles
2025-07-25 17:02:12.938 [info] 'WorkspaceManager[react]' Directory created: node_modules\browserslist
2025-07-25 17:02:12.939 [info] 'WorkspaceManager[react]' Directory created: node_modules\caniuse-lite
2025-07-25 17:02:12.940 [info] 'WorkspaceManager[react]' Directory created: node_modules\chalk
2025-07-25 17:02:12.941 [info] 'WorkspaceManager[react]' Directory created: node_modules\color-convert
2025-07-25 17:02:12.941 [info] 'WorkspaceManager[react]' Directory created: node_modules\color-name
2025-07-25 17:02:12.942 [info] 'WorkspaceManager[react]' Directory created: node_modules\convert-source-map
2025-07-25 17:02:12.943 [info] 'WorkspaceManager[react]' Directory created: node_modules\cross-spawn
2025-07-25 17:02:12.944 [info] 'WorkspaceManager[react]' Directory created: node_modules\csstype
2025-07-25 17:02:12.946 [info] 'WorkspaceManager[react]' Directory created: node_modules\debug
2025-07-25 17:02:12.947 [info] 'WorkspaceManager[react]' Directory created: node_modules\electron-to-chromium
2025-07-25 17:02:12.948 [info] 'WorkspaceManager[react]' Directory created: node_modules\escape-string-regexp
2025-07-25 17:02:12.948 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint
2025-07-25 17:02:12.949 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint-plugin-react-hooks
2025-07-25 17:02:12.950 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint-plugin-react-refresh
2025-07-25 17:02:12.951 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint-scope
2025-07-25 17:02:12.951 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint-visitor-keys
2025-07-25 17:02:12.955 [info] 'WorkspaceManager[react]' Directory created: node_modules\espree
2025-07-25 17:02:12.956 [info] 'WorkspaceManager[react]' Directory created: node_modules\esquery
2025-07-25 17:02:12.957 [info] 'WorkspaceManager[react]' Directory created: node_modules\esutils
2025-07-25 17:02:12.958 [info] 'WorkspaceManager[react]' Directory created: node_modules\fast-deep-equal
2025-07-25 17:02:12.959 [info] 'WorkspaceManager[react]' Directory created: node_modules\fast-json-stable-stringify
2025-07-25 17:02:12.961 [info] 'WorkspaceManager[react]' Directory created: node_modules\file-entry-cache
2025-07-25 17:02:12.962 [info] 'WorkspaceManager[react]' Directory created: node_modules\find-up
2025-07-25 17:02:12.963 [info] 'WorkspaceManager[react]' Directory created: node_modules\gensync
2025-07-25 17:02:12.965 [info] 'WorkspaceManager[react]' Directory created: node_modules\glob-parent
2025-07-25 17:02:12.966 [info] 'WorkspaceManager[react]' Directory created: node_modules\globals
2025-07-25 17:02:12.967 [info] 'WorkspaceManager[react]' Directory created: node_modules\ignore
2025-07-25 17:02:12.967 [info] 'WorkspaceManager[react]' Directory created: node_modules\import-fresh
2025-07-25 17:02:12.968 [info] 'WorkspaceManager[react]' Directory created: node_modules\imurmurhash
2025-07-25 17:02:12.969 [info] 'WorkspaceManager[react]' Directory created: node_modules\is-glob
2025-07-25 17:02:12.970 [info] 'WorkspaceManager[react]' Directory created: node_modules\js-tokens
2025-07-25 17:02:12.971 [info] 'WorkspaceManager[react]' Directory created: node_modules\js-yaml
2025-07-25 17:02:12.972 [info] 'WorkspaceManager[react]' Directory created: node_modules\jsesc
2025-07-25 17:02:12.972 [info] 'WorkspaceManager[react]' Directory created: node_modules\json-schema-traverse
2025-07-25 17:02:12.973 [info] 'WorkspaceManager[react]' Directory created: node_modules\json-stable-stringify-without-jsonify
2025-07-25 17:02:12.973 [info] 'WorkspaceManager[react]' Directory created: node_modules\json5
2025-07-25 17:02:12.974 [info] 'WorkspaceManager[react]' Directory created: node_modules\levn
2025-07-25 17:02:12.974 [info] 'WorkspaceManager[react]' Directory created: node_modules\lodash.merge
2025-07-25 17:02:12.975 [info] 'WorkspaceManager[react]' Directory created: node_modules\lru-cache
2025-07-25 17:02:12.976 [info] 'WorkspaceManager[react]' Directory created: node_modules\minimatch
2025-07-25 17:02:12.976 [info] 'WorkspaceManager[react]' Directory created: node_modules\ms
2025-07-25 17:02:12.982 [info] 'WorkspaceManager[react]' Directory created: node_modules\natural-compare
2025-07-25 17:02:12.984 [info] 'WorkspaceManager[react]' Directory created: node_modules\node-releases
2025-07-25 17:02:12.984 [info] 'WorkspaceManager[react]' Directory created: node_modules\optionator
2025-07-25 17:02:12.985 [info] 'WorkspaceManager[react]' Directory created: node_modules\path-key
2025-07-25 17:02:12.986 [info] 'WorkspaceManager[react]' Directory created: node_modules\picocolors
2025-07-25 17:02:12.986 [info] 'WorkspaceManager[react]' Directory created: node_modules\react
2025-07-25 17:02:12.987 [info] 'WorkspaceManager[react]' Directory created: node_modules\react-dom
2025-07-25 17:02:12.988 [info] 'WorkspaceManager[react]' Directory created: node_modules\react-refresh
2025-07-25 17:02:12.989 [info] 'WorkspaceManager[react]' Directory created: node_modules\semver
2025-07-25 17:02:12.989 [info] 'WorkspaceManager[react]' Directory created: node_modules\shebang-command
2025-07-25 17:02:12.991 [info] 'WorkspaceManager[react]' Directory created: node_modules\strip-json-comments
2025-07-25 17:02:12.992 [info] 'WorkspaceManager[react]' Directory created: node_modules\supports-color
2025-07-25 17:02:12.992 [info] 'WorkspaceManager[react]' Directory created: node_modules\update-browserslist-db
2025-07-25 17:02:12.993 [info] 'WorkspaceManager[react]' Directory created: node_modules\uri-js
2025-07-25 17:02:12.994 [info] 'WorkspaceManager[react]' Directory created: node_modules\vite
2025-07-25 17:02:12.995 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\android-arm64
2025-07-25 17:02:12.996 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\android-x64
2025-07-25 17:02:12.996 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\darwin-arm64
2025-07-25 17:02:12.997 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\darwin-x64
2025-07-25 17:02:12.997 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\freebsd-arm64
2025-07-25 17:02:12.998 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\freebsd-x64
2025-07-25 17:02:12.999 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\linux-arm
2025-07-25 17:02:12.999 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\linux-arm64
2025-07-25 17:02:13.000 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\linux-ia32
2025-07-25 17:02:13.010 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\linux-loong64
2025-07-25 17:02:13.011 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\linux-mips64el
2025-07-25 17:02:13.011 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\linux-ppc64
2025-07-25 17:02:13.012 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\linux-riscv64
2025-07-25 17:02:13.013 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\linux-s390x
2025-07-25 17:02:13.014 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\linux-x64
2025-07-25 17:02:13.014 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\netbsd-arm64
2025-07-25 17:02:13.015 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\netbsd-x64
2025-07-25 17:02:13.016 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\openbsd-arm64
2025-07-25 17:02:13.017 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\openbsd-x64
2025-07-25 17:02:13.017 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\openharmony-arm64
2025-07-25 17:02:13.018 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\sunos-x64
2025-07-25 17:02:13.019 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\win32-arm64
2025-07-25 17:02:13.020 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\win32-ia32
2025-07-25 17:02:13.020 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\win32-x64
2025-07-25 17:02:13.022 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint-community\eslint-utils
2025-07-25 17:02:13.024 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint-community\eslint-utils\node_modules
2025-07-25 17:02:13.025 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint-community\eslint-utils\node_modules\eslint-visitor-keys
2025-07-25 17:02:13.026 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\eslintrc
2025-07-25 17:02:13.036 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\eslintrc\node_modules
2025-07-25 17:02:13.037 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\js
2025-07-25 17:02:13.038 [info] 'WorkspaceManager[react]' Directory created: node_modules\@humanfs\node
2025-07-25 17:02:13.041 [info] 'WorkspaceManager[react]' Directory created: node_modules\@humanwhocodes\module-importer
2025-07-25 17:02:13.041 [info] 'WorkspaceManager[react]' Directory created: node_modules\@jridgewell\gen-mapping
2025-07-25 17:02:13.042 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-android-arm64
2025-07-25 17:02:13.043 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-darwin-arm64
2025-07-25 17:02:13.044 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-darwin-x64
2025-07-25 17:02:13.045 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-freebsd-arm64
2025-07-25 17:02:13.046 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-freebsd-x64
2025-07-25 17:02:13.046 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-linux-arm-gnueabihf
2025-07-25 17:02:13.047 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-linux-arm-musleabihf
2025-07-25 17:02:13.055 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-linux-arm64-gnu
2025-07-25 17:02:13.056 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-linux-arm64-musl
2025-07-25 17:02:13.057 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-linux-loongarch64-gnu
2025-07-25 17:02:13.058 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-linux-powerpc64le-gnu
2025-07-25 17:02:13.059 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-linux-riscv64-gnu
2025-07-25 17:02:13.059 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-linux-riscv64-musl
2025-07-25 17:02:13.060 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-linux-s390x-gnu
2025-07-25 17:02:13.061 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-linux-x64-gnu
2025-07-25 17:02:13.062 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-linux-x64-musl
2025-07-25 17:02:13.062 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-win32-arm64-msvc
2025-07-25 17:02:13.063 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-win32-ia32-msvc
2025-07-25 17:02:13.064 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-win32-x64-msvc
2025-07-25 17:02:13.065 [info] 'WorkspaceManager[react]' Directory created: node_modules\@types\react
2025-07-25 17:02:13.065 [info] 'WorkspaceManager[react]' Directory created: node_modules\@types\react-dom
2025-07-25 17:02:13.066 [info] 'WorkspaceManager[react]' Directory created: node_modules\acorn
2025-07-25 17:02:13.067 [info] 'WorkspaceManager[react]' Directory created: node_modules\acorn-jsx
2025-07-25 17:02:13.069 [info] 'WorkspaceManager[react]' Directory created: node_modules\argparse
2025-07-25 17:02:13.070 [info] 'WorkspaceManager[react]' Directory created: node_modules\balanced-match
2025-07-25 17:02:13.071 [info] 'WorkspaceManager[react]' Directory created: node_modules\brace-expansion
2025-07-25 17:02:13.072 [info] 'WorkspaceManager[react]' Directory created: node_modules\callsites
2025-07-25 17:02:13.073 [info] 'WorkspaceManager[react]' Directory created: node_modules\concat-map
2025-07-25 17:02:13.074 [info] 'WorkspaceManager[react]' Directory created: node_modules\deep-is
2025-07-25 17:02:13.075 [info] 'WorkspaceManager[react]' Directory created: node_modules\esbuild
2025-07-25 17:02:13.076 [info] 'WorkspaceManager[react]' Directory created: node_modules\escalade
2025-07-25 17:02:13.077 [info] 'WorkspaceManager[react]' Directory created: node_modules\esrecurse
2025-07-25 17:02:13.079 [info] 'WorkspaceManager[react]' Directory created: node_modules\estraverse
2025-07-25 17:02:13.081 [info] 'WorkspaceManager[react]' Directory created: node_modules\fast-levenshtein
2025-07-25 17:02:13.083 [info] 'WorkspaceManager[react]' Directory created: node_modules\fdir
2025-07-25 17:02:13.084 [info] 'WorkspaceManager[react]' Directory created: node_modules\flat-cache
2025-07-25 17:02:13.085 [info] 'WorkspaceManager[react]' Directory created: node_modules\flatted
2025-07-25 17:02:13.086 [info] 'WorkspaceManager[react]' Directory created: node_modules\fsevents
2025-07-25 17:02:13.087 [info] 'WorkspaceManager[react]' Directory created: node_modules\has-flag
2025-07-25 17:02:13.088 [info] 'WorkspaceManager[react]' Directory created: node_modules\is-extglob
2025-07-25 17:02:13.088 [info] 'WorkspaceManager[react]' Directory created: node_modules\isexe
2025-07-25 17:02:13.089 [info] 'WorkspaceManager[react]' Directory created: node_modules\json-buffer
2025-07-25 17:02:13.090 [info] 'WorkspaceManager[react]' Directory created: node_modules\keyv
2025-07-25 17:02:13.091 [info] 'WorkspaceManager[react]' Directory created: node_modules\locate-path
2025-07-25 17:02:13.092 [info] 'WorkspaceManager[react]' Directory created: node_modules\nanoid
2025-07-25 17:02:13.093 [info] 'WorkspaceManager[react]' Directory created: node_modules\p-limit
2025-07-25 17:02:13.093 [info] 'WorkspaceManager[react]' Directory created: node_modules\p-locate
2025-07-25 17:02:13.094 [info] 'WorkspaceManager[react]' Directory created: node_modules\parent-module
2025-07-25 17:02:13.095 [info] 'WorkspaceManager[react]' Directory created: node_modules\path-exists
2025-07-25 17:02:13.096 [info] 'WorkspaceManager[react]' Directory created: node_modules\picomatch
2025-07-25 17:02:13.098 [info] 'WorkspaceManager[react]' Directory created: node_modules\postcss
2025-07-25 17:02:13.100 [info] 'WorkspaceManager[react]' Directory created: node_modules\prelude-ls
2025-07-25 17:02:13.101 [info] 'WorkspaceManager[react]' Directory created: node_modules\punycode
2025-07-25 17:02:13.101 [info] 'WorkspaceManager[react]' Directory created: node_modules\resolve-from
2025-07-25 17:02:13.102 [info] 'WorkspaceManager[react]' Directory created: node_modules\rollup
2025-07-25 17:02:13.106 [info] 'WorkspaceManager[react]' Directory created: node_modules\scheduler
2025-07-25 17:02:13.110 [info] 'WorkspaceManager[react]' Directory created: node_modules\shebang-regex
2025-07-25 17:02:13.111 [info] 'WorkspaceManager[react]' Directory created: node_modules\source-map-js
2025-07-25 17:02:13.111 [info] 'WorkspaceManager[react]' Directory created: node_modules\tinyglobby
2025-07-25 17:02:13.112 [info] 'WorkspaceManager[react]' Directory created: node_modules\type-check
2025-07-25 17:02:13.114 [info] 'WorkspaceManager[react]' Directory created: node_modules\word-wrap
2025-07-25 17:02:13.115 [info] 'WorkspaceManager[react]' Directory created: node_modules\yallist
2025-07-25 17:02:13.116 [info] 'WorkspaceManager[react]' Directory created: node_modules\yocto-queue
2025-07-25 17:02:13.141 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\eslintrc\node_modules\globals
2025-07-25 17:02:13.146 [info] 'WorkspaceManager[react]' Directory created: node_modules\@humanfs\node\node_modules
2025-07-25 17:02:13.148 [info] 'WorkspaceManager[react]' Directory created: node_modules\@humanfs\node\node_modules\@humanwhocodes
2025-07-25 17:02:13.149 [info] 'WorkspaceManager[react]' Directory created: node_modules\@humanfs\node\node_modules\@humanwhocodes\retry
2025-07-25 17:02:13.737 [info] 'WorkspaceManager[react]' Directory created: node_modules\ajv\scripts
2025-07-25 17:02:13.900 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1857.9167,"timestamp":"2025-07-25T16:02:13.869Z"}]
2025-07-25 17:02:14.310 [info] 'WorkspaceManager[react]' Directory created: node_modules\cross-spawn\lib
2025-07-25 17:02:14.372 [info] 'WorkspaceManager[react]' Directory created: node_modules\debug\src
2025-07-25 17:02:14.373 [info] 'WorkspaceManager[react]' Directory created: node_modules\deep-is\example
2025-07-25 17:02:14.374 [info] 'WorkspaceManager[react]' Directory created: node_modules\escalade\dist
2025-07-25 17:02:14.376 [info] 'WorkspaceManager[react]' Directory created: node_modules\fast-deep-equal\es6
2025-07-25 17:02:14.377 [info] 'WorkspaceManager[react]' Directory created: node_modules\fast-json-stable-stringify\test
2025-07-25 17:02:14.378 [info] 'WorkspaceManager[react]' Directory created: node_modules\fdir\dist
2025-07-25 17:02:14.379 [info] 'WorkspaceManager[react]' Directory created: node_modules\js-yaml\lib
2025-07-25 17:02:14.379 [info] 'WorkspaceManager[react]' Directory created: node_modules\js-yaml\lib\type
2025-07-25 17:02:14.380 [info] 'WorkspaceManager[react]' Directory created: node_modules\source-map-js\lib
2025-07-25 17:02:14.381 [info] 'WorkspaceManager[react]' Directory created: node_modules\tinyglobby\dist
2025-07-25 17:02:14.386 [info] 'WorkspaceManager[react]' Directory created: node_modules\uri-js\dist
2025-07-25 17:02:14.388 [info] 'WorkspaceManager[react]' Directory created: node_modules\uri-js\dist\esnext
2025-07-25 17:02:14.389 [info] 'WorkspaceManager[react]' Directory created: node_modules\which\bin
2025-07-25 17:02:14.637 [info] 'WorkspaceManager[react]' Directory created: node_modules\chalk\source
2025-07-25 17:02:14.692 [info] 'WorkspaceManager[react]' Directory created: node_modules\uri-js\dist\esnext\schemes
2025-07-25 17:02:15.018 [info] 'WorkspaceManager[react]' Directory created: node_modules\semver\bin
2025-07-25 17:02:15.502 [info] 'WorkspaceManager[react]' Directory created: node_modules\cross-spawn\lib\util
2025-07-25 17:02:15.617 [info] 'WorkspaceManager[react]' Directory created: node_modules\deep-is\test
2025-07-25 17:02:15.619 [info] 'WorkspaceManager[react]' Directory created: node_modules\escalade\sync
2025-07-25 17:02:15.620 [info] 'WorkspaceManager[react]' Directory created: node_modules\fast-json-stable-stringify\benchmark
2025-07-25 17:02:16.059 [info] 'WorkspaceManager[react]' Directory created: node_modules\acorn\bin
2025-07-25 17:02:16.207 [info] 'WorkspaceManager[react]' Directory created: node_modules\esbuild\bin
2025-07-25 17:02:16.208 [info] 'WorkspaceManager[react]' Directory created: node_modules\esutils\lib
2025-07-25 17:02:16.211 [info] 'WorkspaceManager[react]' Directory created: node_modules\fdir\dist\builder
2025-07-25 17:02:16.257 [info] 'WorkspaceManager[react]' Directory created: node_modules\postcss\lib
2025-07-25 17:02:16.307 [info] 'WorkspaceManager[react]' Directory created: node_modules\rollup\dist
2025-07-25 17:02:16.729 [info] 'WorkspaceManager[react]' Directory created: node_modules\argparse\lib
2025-07-25 17:02:16.833 [info] 'WorkspaceManager[react]' Directory created: node_modules\balanced-match\.github
2025-07-25 17:02:16.835 [info] 'WorkspaceManager[react]' Directory created: node_modules\esquery\dist
2025-07-25 17:02:16.836 [info] 'WorkspaceManager[react]' Directory created: node_modules\keyv\src
2025-07-25 17:02:16.837 [info] 'WorkspaceManager[react]' Directory created: node_modules\nanoid\async
2025-07-25 17:02:16.840 [info] 'WorkspaceManager[react]' Directory created: node_modules\picomatch\lib
2025-07-25 17:02:16.842 [info] 'WorkspaceManager[react]' Directory created: node_modules\prelude-ls\lib
2025-07-25 17:02:16.844 [info] 'WorkspaceManager[react]' Directory created: node_modules\rollup\dist\bin
2025-07-25 17:02:16.845 [info] 'WorkspaceManager[react]' Directory created: node_modules\type-check\lib
2025-07-25 17:02:17.129 [info] 'WorkspaceManager[react]' Directory created: node_modules\concat-map\example
2025-07-25 17:02:17.212 [info] 'WorkspaceManager[react]' Directory created: node_modules\fast-json-stable-stringify\example
2025-07-25 17:02:17.214 [info] 'WorkspaceManager[react]' Directory created: node_modules\fdir\dist\api
2025-07-25 17:02:17.221 [info] 'WorkspaceManager[react]' Directory created: node_modules\flatted\cjs
2025-07-25 17:02:17.223 [info] 'WorkspaceManager[react]' Directory created: node_modules\js-yaml\lib\schema
2025-07-25 17:02:17.226 [info] 'WorkspaceManager[react]' Directory created: node_modules\levn\lib
2025-07-25 17:02:17.227 [info] 'WorkspaceManager[react]' Directory created: node_modules\optionator\lib
2025-07-25 17:02:17.435 [error] 'AugmentExtensionSidecar' API request 92ad0dd4-2e67-4dd9-a76d-5a76930bed47 to https://d1.api.augmentcode.com/chat-stream failed: The operation was aborted due to timeout
2025-07-25 17:02:17.435 [error] 'AugmentExtensionSidecar' TimeoutError: The operation was aborted due to timeout
    at node:internal/deps/undici/undici:13510:13
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at runNextTicks (node:internal/process/task_queues:69:3)
    at listOnTimeout (node:internal/timers:549:9)
    at processTimers (node:internal/timers:523:7)
    at VH.OJ.globalThis.fetch (file:///c:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:173:21986)
    at t8 (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:7027)
    at VH.callApiStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:637:4046)
    at VH.callApiStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:57628)
    at VH.chatStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:18105)
    at e.startChatStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:842:36009)
    at e.chatStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:842:34175)
    at LO.onUserSendMessage (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:1988:3278)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:644:5064
2025-07-25 17:02:17.846 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\object-schema\dist
2025-07-25 17:02:17.934 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\object-schema\dist\cjs
2025-07-25 17:02:17.935 [info] 'WorkspaceManager[react]' Directory created: node_modules\@humanfs\core\src
2025-07-25 17:02:17.936 [info] 'WorkspaceManager[react]' Directory created: node_modules\acorn\dist
2025-07-25 17:02:17.937 [info] 'WorkspaceManager[react]' Directory created: node_modules\ajv\lib
2025-07-25 17:02:17.939 [info] 'WorkspaceManager[react]' Directory created: node_modules\ajv\lib\dot
2025-07-25 17:02:17.943 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint-scope\dist
2025-07-25 17:02:17.948 [info] 'WorkspaceManager[react]' Directory created: node_modules\espree\dist
2025-07-25 17:02:17.950 [info] 'WorkspaceManager[react]' Directory created: node_modules\json-schema-traverse\spec
2025-07-25 17:02:17.952 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint-visitor-keys\dist
2025-07-25 17:02:18.180 [info] 'WorkspaceManager[react]' Directory created: node_modules\@humanwhocodes\retry\dist
2025-07-25 17:02:18.201 [info] 'WorkspaceManager[react]' Directory created: node_modules\concat-map\test
2025-07-25 17:02:18.202 [info] 'WorkspaceManager[react]' Directory created: node_modules\esbuild\lib
2025-07-25 17:02:18.204 [info] 'WorkspaceManager[react]' Directory created: node_modules\flatted\esm
2025-07-25 17:02:18.205 [info] 'WorkspaceManager[react]' Directory created: node_modules\scheduler\cjs
2025-07-25 17:02:18.283 [error] 'AugmentExtension' API request 60bcca69-37a6-43b9-99a3-54c12135b6ea to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:02:18.283 [error] 'AugmentExtension' Dropping error report "chat-stream call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-25 17:02:18.285 [error] 'ChatApp' Chat stream failed: Error: The operation was aborted due to timeout
Error: The operation was aborted due to timeout
    at e.transientIssue (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:21726)
    at VH.callApiStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:637:4279)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at runNextTicks (node:internal/process/task_queues:69:3)
    at listOnTimeout (node:internal/timers:549:9)
    at processTimers (node:internal/timers:523:7)
    at VH.callApiStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:57628)
    at VH.chatStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:18105)
    at e.startChatStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:842:36009)
    at e.chatStream (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:842:34175)
    at LO.onUserSendMessage (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:1988:3278)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:644:5064
2025-07-25 17:02:18.800 [info] 'WorkspaceManager[react]' Directory created: node_modules\fdir\dist\api\functions
2025-07-25 17:02:18.859 [info] 'WorkspaceManager[react]' Directory created: node_modules\isexe\test
2025-07-25 17:02:18.885 [error] 'AugmentExtension' API request 5a5676f3-79f5-457e-85a8-c362bcd0ebde to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:02:18.885 [error] 'AugmentExtension' Dropping error report "chat_stream_failed" due to error: This operation was aborted
2025-07-25 17:02:18.991 [info] 'WorkspaceManager[react]' Directory created: node_modules\json-schema-traverse\spec\fixtures
2025-07-25 17:02:19.503 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\eslintrc\dist
2025-07-25 17:02:19.635 [info] 'WorkspaceManager[react]' Directory created: node_modules\@humanfs\node\src
2025-07-25 17:02:19.688 [info] 'WorkspaceManager[react]' Directory created: node_modules\json-buffer\test
2025-07-25 17:02:19.756 [info] 'WorkspaceManager[react]' Directory created: node_modules\json-stable-stringify-without-jsonify\example
2025-07-25 17:02:19.813 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\config-helpers\dist
2025-07-25 17:02:19.884 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\config-helpers\dist\cjs
2025-07-25 17:02:19.886 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\plugin-kit\dist
2025-07-25 17:02:19.889 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint-visitor-keys\lib
2025-07-25 17:02:19.890 [info] 'WorkspaceManager[react]' Directory created: node_modules\nanoid\non-secure
2025-07-25 17:02:19.891 [info] 'WorkspaceManager[react]' Directory created: node_modules\node-releases\data
2025-07-25 17:02:19.985 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\object-schema\dist\esm
2025-07-25 17:02:20.042 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\plugin-kit\dist\cjs
2025-07-25 17:02:20.043 [info] 'WorkspaceManager[react]' Directory created: node_modules\@jridgewell\resolve-uri\dist
2025-07-25 17:02:20.044 [info] 'WorkspaceManager[react]' Directory created: node_modules\@jridgewell\sourcemap-codec\types
2025-07-25 17:02:20.061 [info] 'WorkspaceManager[react]' Directory created: node_modules\node-releases\data\processed
2025-07-25 17:02:20.130 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\config-array\dist
2025-07-25 17:02:20.167 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\config-array\dist\cjs
2025-07-25 17:02:20.181 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\core\dist
2025-07-25 17:02:20.182 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\core\dist\cjs
2025-07-25 17:02:20.183 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint-scope\lib
2025-07-25 17:02:20.184 [info] 'WorkspaceManager[react]' Directory created: node_modules\flat-cache\src
2025-07-25 17:02:20.185 [info] 'WorkspaceManager[react]' Directory created: node_modules\uri-js\dist\es5
2025-07-25 17:02:20.237 [info] 'WorkspaceManager[react]' Directory created: node_modules\@humanwhocodes\module-importer\dist
2025-07-25 17:02:20.266 [info] 'WorkspaceManager[react]' Directory created: node_modules\espree\lib
2025-07-25 17:02:20.267 [info] 'WorkspaceManager[react]' Directory created: node_modules\jsesc\bin
2025-07-25 17:02:20.425 [info] 'WorkspaceManager[react]' Directory created: node_modules\caniuse-lite\data
2025-07-25 17:02:20.493 [info] 'WorkspaceManager[react]' Directory created: node_modules\nanoid\url-alphabet
2025-07-25 17:02:20.495 [info] 'WorkspaceManager[react]' Directory created: node_modules\rollup\dist\shared
2025-07-25 17:02:20.841 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\helper-globals\data
2025-07-25 17:02:20.901 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\helper-module-imports\lib
2025-07-25 17:02:20.902 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\helper-validator-option\lib
2025-07-25 17:02:20.903 [info] 'WorkspaceManager[react]' Directory created: node_modules\caniuse-lite\data\features
2025-07-25 17:02:20.910 [info] 'WorkspaceManager[react]' Directory created: node_modules\gensync\test
2025-07-25 17:02:21.252 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\helper-string-parser\lib
2025-07-25 17:02:21.310 [info] 'WorkspaceManager[react]' Directory created: node_modules\@humanwhocodes\module-importer\src
2025-07-25 17:02:21.312 [info] 'WorkspaceManager[react]' Directory created: node_modules\jsesc\man
2025-07-25 17:02:21.824 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\helper-validator-identifier\lib
2025-07-25 17:02:21.944 [info] 'WorkspaceManager[react]' Directory created: node_modules\@jridgewell\gen-mapping\types
2025-07-25 17:02:21.946 [info] 'WorkspaceManager[react]' Directory created: node_modules\ajv\lib\dotjs
2025-07-25 17:02:21.947 [info] 'WorkspaceManager[react]' Directory created: node_modules\json5\lib
2025-07-25 17:02:21.949 [info] 'WorkspaceManager[react]' Directory created: node_modules\nanoid\bin
2025-07-25 17:02:21.952 [info] 'WorkspaceManager[react]' Directory created: node_modules\node-releases\data\release-schedule
2025-07-25 17:02:21.953 [info] 'WorkspaceManager[react]' Directory created: node_modules\rollup\dist\es
2025-07-25 17:02:22.025 [info] 'WorkspaceManager[react]' Directory created: node_modules\@jridgewell\trace-mapping\types
2025-07-25 17:02:22.094 [info] 'WorkspaceManager[react]' Directory created: node_modules\@humanfs\core\dist
2025-07-25 17:02:22.095 [info] 'WorkspaceManager[react]' Directory created: node_modules\flatted\php
2025-07-25 17:02:22.096 [info] 'WorkspaceManager[react]' Directory created: node_modules\json5\dist
2025-07-25 17:02:22.193 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\config-array\dist\cjs\std__path
2025-07-25 17:02:22.230 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\config-helpers\dist\esm
2025-07-25 17:02:22.232 [info] 'WorkspaceManager[react]' Directory created: node_modules\@humanfs\node\dist
2025-07-25 17:02:22.239 [info] 'WorkspaceManager[react]' Directory created: node_modules\@jridgewell\sourcemap-codec\dist
2025-07-25 17:02:22.240 [info] 'WorkspaceManager[react]' Directory created: node_modules\@types\react\ts5.0
2025-07-25 17:02:22.375 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\template\lib
2025-07-25 17:02:22.424 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\plugin-kit\dist\esm
2025-07-25 17:02:22.623 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\compat-data\data
2025-07-25 17:02:22.657 [info] 'WorkspaceManager[react]' Directory created: node_modules\js-yaml\bin
2025-07-25 17:02:23.252 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\core\dist\esm
2025-07-25 17:02:23.306 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\eslintrc\lib
2025-07-25 17:02:23.321 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\eslintrc\lib\shared
2025-07-25 17:02:23.346 [info] 'WorkspaceManager[react]' Directory created: node_modules\caniuse-lite\data\regions
2025-07-25 17:02:23.349 [info] 'WorkspaceManager[react]' Directory created: node_modules\flatted\python
2025-07-25 17:02:23.354 [info] 'WorkspaceManager[react]' Directory created: node_modules\json-stable-stringify-without-jsonify\test
2025-07-25 17:02:23.483 [info] 'WorkspaceManager[react]' Directory created: node_modules\js-yaml\dist
2025-07-25 17:02:30.875 [info] 'WorkspaceManager[react]' Directory created: node_modules\@ampproject\remapping\dist
2025-07-25 17:02:31.078 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\types\lib
2025-07-25 17:02:31.099 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\types\lib\asserts
2025-07-25 17:02:31.101 [info] 'WorkspaceManager[react]' Directory created: node_modules\flatted\types
2025-07-25 17:02:31.118 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 7173 msec late.
2025-07-25 17:02:31.499 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\traverse\lib
2025-07-25 17:02:31.545 [info] 'WorkspaceManager[react]' Directory created: node_modules\@jridgewell\gen-mapping\dist
2025-07-25 17:02:31.549 [info] 'WorkspaceManager[react]' Directory created: node_modules\vite\bin
2025-07-25 17:02:31.669 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\helper-compilation-targets\lib
2025-07-25 17:02:31.730 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\helper-module-transforms\lib
2025-07-25 17:02:31.734 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\helper-plugin-utils\lib
2025-07-25 17:02:31.736 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\helpers\lib
2025-07-25 17:02:31.737 [info] 'WorkspaceManager[react]' Directory created: node_modules\@jridgewell\resolve-uri\dist\types
2025-07-25 17:02:31.739 [info] 'WorkspaceManager[react]' Directory created: node_modules\ajv\dist
2025-07-25 17:02:31.745 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint-plugin-react-hooks\cjs
2025-07-25 17:02:31.747 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint\lib
2025-07-25 17:02:31.752 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint\lib\rules
2025-07-25 17:02:31.753 [info] 'WorkspaceManager[react]' Directory created: node_modules\react-refresh\cjs
2025-07-25 17:02:31.833 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\code-frame\lib
2025-07-25 17:02:31.893 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\core\lib
2025-07-25 17:02:31.902 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\core\lib\config
2025-07-25 17:02:31.905 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\parser\bin
2025-07-25 17:02:31.906 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\plugin-transform-react-jsx-self\lib
2025-07-25 17:02:31.909 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\plugin-transform-react-jsx-source\lib
2025-07-25 17:02:31.910 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint-community\eslint-utils\node_modules\eslint-visitor-keys\dist
2025-07-25 17:02:31.912 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\js\src
2025-07-25 17:02:31.922 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\js\src\configs
2025-07-25 17:02:31.924 [info] 'WorkspaceManager[react]' Directory created: node_modules\@humanfs\node\node_modules\@humanwhocodes\retry\dist
2025-07-25 17:02:31.928 [info] 'WorkspaceManager[react]' Directory created: node_modules\@vitejs\plugin-react\dist
2025-07-25 17:02:31.930 [info] 'WorkspaceManager[react]' Directory created: node_modules\vite\dist
2025-07-25 17:02:32.103 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\generator\lib
2025-07-25 17:02:32.184 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\parser\lib
2025-07-25 17:02:32.186 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\types\lib\asserts\generated
2025-07-25 17:02:32.187 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint\lib\shared
2025-07-25 17:02:32.188 [info] 'WorkspaceManager[react]' Directory created: node_modules\fast-json-stable-stringify\.github
2025-07-25 17:02:32.189 [info] 'WorkspaceManager[react]' Directory created: node_modules\vite\dist\node
2025-07-25 17:02:32.387 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\generator\lib\generators
2025-07-25 17:02:32.576 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint-community\eslint-utils\node_modules\eslint-visitor-keys\lib
2025-07-25 17:02:32.577 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\config-array\dist\esm
2025-07-25 17:02:32.579 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\eslintrc\lib\config-array
2025-07-25 17:02:32.580 [info] 'WorkspaceManager[react]' Directory created: node_modules\@jridgewell\trace-mapping\dist
2025-07-25 17:02:32.581 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint\messages
2025-07-25 17:02:32.583 [info] 'WorkspaceManager[react]' Directory created: node_modules\react-dom\cjs
2025-07-25 17:02:32.584 [info] 'WorkspaceManager[react]' Directory created: node_modules\vite\dist\node\chunks
2025-07-25 17:02:32.946 [info] 'WorkspaceManager[react]' Directory created: node_modules\@ampproject\remapping\dist\types
2025-07-25 17:02:33.023 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\types\lib\ast-types
2025-07-25 17:02:33.025 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\types\lib\ast-types\generated
2025-07-25 17:02:33.026 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\config-array\dist\esm\std__path
2025-07-25 17:02:33.027 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\js\types
2025-07-25 17:02:33.028 [info] 'WorkspaceManager[react]' Directory created: node_modules\@types\react\ts5.0\v18
2025-07-25 17:02:33.031 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint\lib\linter
2025-07-25 17:02:33.032 [info] 'WorkspaceManager[react]' Directory created: node_modules\react\cjs
2025-07-25 17:02:33.182 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\traverse\lib\path
2025-07-25 17:02:33.260 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\eslintrc\conf
2025-07-25 17:02:33.261 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rolldown\pluginutils\dist
2025-07-25 17:02:33.263 [info] 'WorkspaceManager[react]' Directory created: node_modules\caniuse-lite\dist
2025-07-25 17:02:33.266 [info] 'WorkspaceManager[react]' Directory created: node_modules\caniuse-lite\dist\unpacker
2025-07-25 17:02:33.592 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\helpers\lib\helpers
2025-07-25 17:02:33.758 [info] 'WorkspaceManager[react]' Directory created: node_modules\ajv\lib\compile
2025-07-25 17:02:33.759 [info] 'WorkspaceManager[react]' Directory created: node_modules\rollup\dist\es\shared
2025-07-25 17:02:33.760 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\core\lib\config\files
2025-07-25 17:02:33.763 [info] 'WorkspaceManager[react]' Directory created: node_modules\@types\react\ts5.0\v18\ts5.0
2025-07-25 17:02:34.133 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\types\lib\builders
2025-07-25 17:02:34.194 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\types\lib\builders\flow
2025-07-25 17:02:34.644 [info] 'WorkspaceManager[react]' Directory created: node_modules\@jridgewell\sourcemap-codec\src
2025-07-25 17:02:34.697 [info] 'WorkspaceManager[react]' Directory created: node_modules\@types\react-dom\test-utils
2025-07-25 17:02:34.698 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint\lib\rules\utils
2025-07-25 17:02:35.161 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\types\lib\builders\generated
2025-07-25 17:02:35.216 [info] 'WorkspaceManager[react]' Directory created: node_modules\@jridgewell\gen-mapping\src
2025-07-25 17:02:35.218 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint\lib\languages
2025-07-25 17:02:35.648 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint\lib\languages\js
2025-07-25 17:02:35.714 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint\lib\languages\js\source-code
2025-07-25 17:02:35.734 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint\lib\languages\js\source-code\token-store
2025-07-25 17:02:36.445 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\traverse\lib\path\inference
2025-07-25 17:02:37.680 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint\lib\cli-engine
2025-07-25 17:02:37.923 [info] 'WorkspaceManager[react]' Directory created: node_modules\@eslint\eslintrc\lib\types
2025-07-25 17:02:38.188 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\types\lib\builders\react
2025-07-25 17:02:38.422 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\core\lib\config\helpers
2025-07-25 17:02:38.436 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint\lib\linter\code-path-analysis
2025-07-25 17:02:38.526 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\parser\typings
2025-07-25 17:02:39.148 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\generator\lib\node
2025-07-25 17:02:39.182 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\traverse\lib\path\lib
2025-07-25 17:02:39.185 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\types\lib\builders\typescript
2025-07-25 17:02:39.187 [info] 'WorkspaceManager[react]' Directory created: node_modules\@jridgewell\trace-mapping\src
2025-07-25 17:02:39.643 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\types\lib\clone
2025-07-25 17:02:39.711 [info] 'WorkspaceManager[react]' Directory created: node_modules\vite\misc
2025-07-25 17:02:40.179 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\types\lib\comments
2025-07-25 17:02:40.232 [info] 'WorkspaceManager[react]' Directory created: node_modules\ajv\lib\refs
2025-07-25 17:02:40.237 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint\lib\config
2025-07-25 17:02:40.238 [info] 'WorkspaceManager[react]' Directory created: node_modules\vite\types
2025-07-25 17:02:40.639 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\traverse\lib\scope
2025-07-25 17:02:41.028 [info] 'WorkspaceManager[react]' Directory created: node_modules\vite\dist\client
2025-07-25 17:02:41.634 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\core\lib\config\validation
2025-07-25 17:02:41.727 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\traverse\lib\scope\lib
2025-07-25 17:02:41.728 [info] 'WorkspaceManager[react]' Directory created: node_modules\vite\types\internal
2025-07-25 17:02:42.213 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\types\lib\constants
2025-07-25 17:02:42.222 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\types\lib\constants\generated
2025-07-25 17:02:42.712 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint\conf
2025-07-25 17:02:42.992 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\core\lib\errors
2025-07-25 17:02:43.013 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\types\lib\converters
2025-07-25 17:02:43.961 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\core\lib\gensync-utils
2025-07-25 17:02:43.971 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint\lib\eslint
2025-07-25 17:02:44.382 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint\bin
2025-07-25 17:02:46.609 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\core\lib\parser
2025-07-25 17:02:47.129 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\core\lib\parser\util
2025-07-25 17:02:47.173 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\core\lib\tools
2025-07-25 17:02:47.481 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\types\lib\definitions
2025-07-25 17:02:47.712 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\core\lib\transformation
2025-07-25 17:02:47.916 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\core\lib\transformation\file
2025-07-25 17:02:48.616 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\core\lib\transformation\util
2025-07-25 17:02:48.660 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint\lib\cli-engine\formatters
2025-07-25 17:02:48.972 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\core\lib\vendor
2025-07-25 17:02:49.107 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\core\src
2025-07-25 17:02:49.141 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\core\src\config
2025-07-25 17:02:49.142 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\core\src\config\files
2025-07-25 17:02:49.755 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint\lib\rule-tester
2025-07-25 17:02:50.036 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\types\lib\modifications
2025-07-25 17:02:50.120 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\types\lib\modifications\flow
2025-07-25 17:02:50.134 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint\lib\rules\utils\unicode
2025-07-25 17:02:50.814 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\types\lib\modifications\typescript
2025-07-25 17:02:50.990 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\types\lib\retrievers
2025-07-25 17:02:51.551 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\types\lib\traverse
2025-07-25 17:02:51.912 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\types\lib\utils
2025-07-25 17:02:52.203 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\types\lib\utils\react
2025-07-25 17:02:52.471 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\types\lib\validators
2025-07-25 17:02:52.519 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\types\lib\validators\generated
2025-07-25 17:02:53.873 [info] 'WorkspaceManager[react]' Directory created: node_modules\@babel\types\lib\validators\react
2025-07-25 17:02:57.623 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint\lib\services
2025-07-25 17:03:00.160 [info] 'WorkspaceManager[react]' Directory created: node_modules\eslint\lib\types
2025-07-25 17:03:03.112 [info] 'WorkspaceManager[react]' Directory created: node_modules\caniuse-lite\dist\lib
2025-07-25 17:03:05.011 [info] 'WorkspaceManager[react]' Directory created: node_modules\.bin
2025-07-25 17:03:08.115 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-win32-arm64-msvc
2025-07-25 17:03:10.936 [info] 'WorkspaceManager[react]' Directory removed: node_modules\fsevents
2025-07-25 17:03:10.936 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\aix-ppc64
2025-07-25 17:03:10.936 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\linux-arm
2025-07-25 17:03:10.936 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\linux-x64
2025-07-25 17:03:10.936 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\sunos-x64
2025-07-25 17:03:10.936 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\darwin-x64
2025-07-25 17:03:10.936 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\linux-ia32
2025-07-25 17:03:10.936 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\netbsd-x64
2025-07-25 17:03:10.936 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\win32-ia32
2025-07-25 17:03:10.936 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\android-arm
2025-07-25 17:03:10.937 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\android-x64
2025-07-25 17:03:10.937 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\freebsd-x64
2025-07-25 17:03:10.937 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\linux-arm64
2025-07-25 17:03:10.937 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\linux-ppc64
2025-07-25 17:03:10.937 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\linux-s390x
2025-07-25 17:03:10.937 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\openbsd-x64
2025-07-25 17:03:10.937 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\win32-arm64
2025-07-25 17:03:10.937 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\darwin-arm64
2025-07-25 17:03:10.937 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\netbsd-arm64
2025-07-25 17:03:10.937 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\android-arm64
2025-07-25 17:03:10.938 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\freebsd-arm64
2025-07-25 17:03:10.938 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\linux-loong64
2025-07-25 17:03:10.938 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\linux-riscv64
2025-07-25 17:03:10.938 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\openbsd-arm64
2025-07-25 17:03:10.938 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\linux-mips64el
2025-07-25 17:03:10.938 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-darwin-x64
2025-07-25 17:03:10.938 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\openharmony-arm64
2025-07-25 17:03:10.938 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-freebsd-x64
2025-07-25 17:03:10.938 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-darwin-arm64
2025-07-25 17:03:10.938 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-android-arm64
2025-07-25 17:03:10.938 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-freebsd-arm64
2025-07-25 17:03:10.938 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-linux-x64-gnu
2025-07-25 17:03:10.938 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-linux-x64-musl
2025-07-25 17:03:10.938 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-linux-arm64-gnu
2025-07-25 17:03:10.938 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-linux-s390x-gnu
2025-07-25 17:03:10.938 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-win32-ia32-msvc
2025-07-25 17:03:10.939 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-android-arm-eabi
2025-07-25 17:03:10.939 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-linux-arm64-musl
2025-07-25 17:03:10.939 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-linux-riscv64-gnu
2025-07-25 17:03:10.939 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-linux-riscv64-musl
2025-07-25 17:03:10.939 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-linux-arm-gnueabihf
2025-07-25 17:03:10.939 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-linux-arm-musleabihf
2025-07-25 17:03:10.939 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-linux-loongarch64-gnu
2025-07-25 17:03:10.939 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-linux-powerpc64le-gnu
2025-07-25 17:03:10.967 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 2751 msec late.
2025-07-25 17:03:15.883 [info] 'ViewTool' Tool called with path: . and view_range: undefined
2025-07-25 17:03:15.885 [info] 'ViewTool' Listing directory: . (depth: 2, showHidden: false)
2025-07-25 17:03:43.067 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1046.4494,"timestamp":"2025-07-25T16:03:43.057Z"}]
2025-07-25 17:04:01.258 [error] 'AugmentExtension' API request 2ed11acc-fa00-49cb-886b-c0b9ede8f1ee to https://d1.api.augmentcode.com/client-metrics failed: This operation was aborted
2025-07-25 17:04:01.805 [error] 'AugmentExtension' API request 54fb3d38-644b-455f-a8ed-1c63c508179d to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:04:01.805 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus unavailable" due to error: This operation was aborted
2025-07-25 17:04:01.806 [error] 'ClientMetricsReporter' Error uploading metrics: Error: This operation was aborted Error: This operation was aborted
    at e.transientIssue (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:21726)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:12726)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at runNextTicks (node:internal/process/task_queues:69:3)
    at listOnTimeout (node:internal/timers:549:9)
    at processTimers (node:internal/timers:523:7)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:04:01.806 [info] 'ClientMetricsReporter' Operation failed with error Error: This operation was aborted, retrying in 100 ms; retries = 0
2025-07-25 17:04:02.905 [info] 'ClientMetricsReporter' Operation succeeded after 1 transient failures
2025-07-25 17:05:17.300 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\android-arm64
2025-07-25 17:05:17.465 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\aix-ppc64
2025-07-25 17:05:17.501 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\android-arm
2025-07-25 17:05:17.502 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\android-x64
2025-07-25 17:05:17.502 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\darwin-arm64
2025-07-25 17:05:17.503 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\darwin-x64
2025-07-25 17:05:17.503 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\freebsd-arm64
2025-07-25 17:05:17.504 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\freebsd-x64
2025-07-25 17:05:17.504 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\linux-arm
2025-07-25 17:05:17.505 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\linux-arm64
2025-07-25 17:05:17.506 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\linux-ia32
2025-07-25 17:05:17.506 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\linux-loong64
2025-07-25 17:05:17.507 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\linux-mips64el
2025-07-25 17:05:17.507 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\linux-ppc64
2025-07-25 17:05:17.507 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\linux-riscv64
2025-07-25 17:05:17.508 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\linux-s390x
2025-07-25 17:05:17.508 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\linux-x64
2025-07-25 17:05:17.509 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\netbsd-arm64
2025-07-25 17:05:17.509 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\netbsd-x64
2025-07-25 17:05:17.509 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\openbsd-arm64
2025-07-25 17:05:17.510 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\openbsd-x64
2025-07-25 17:05:17.511 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\openharmony-arm64
2025-07-25 17:05:17.511 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\sunos-x64
2025-07-25 17:05:17.511 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\win32-arm64
2025-07-25 17:05:17.512 [info] 'WorkspaceManager[react]' Directory created: node_modules\@esbuild\win32-ia32
2025-07-25 17:05:17.512 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-android-arm-eabi
2025-07-25 17:05:17.514 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-android-arm64
2025-07-25 17:05:17.515 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-darwin-arm64
2025-07-25 17:05:17.515 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-darwin-x64
2025-07-25 17:05:17.516 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-freebsd-arm64
2025-07-25 17:05:17.517 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-freebsd-x64
2025-07-25 17:05:17.520 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-linux-arm-gnueabihf
2025-07-25 17:05:17.521 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-linux-arm-musleabihf
2025-07-25 17:05:17.523 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-linux-arm64-gnu
2025-07-25 17:05:17.523 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-linux-arm64-musl
2025-07-25 17:05:17.524 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-linux-loongarch64-gnu
2025-07-25 17:05:17.525 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-linux-powerpc64le-gnu
2025-07-25 17:05:17.526 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-linux-riscv64-gnu
2025-07-25 17:05:17.530 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-linux-riscv64-musl
2025-07-25 17:05:17.532 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-linux-s390x-gnu
2025-07-25 17:05:17.533 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-linux-x64-gnu
2025-07-25 17:05:17.533 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-linux-x64-musl
2025-07-25 17:05:17.534 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-win32-arm64-msvc
2025-07-25 17:05:17.535 [info] 'WorkspaceManager[react]' Directory created: node_modules\@rollup\rollup-win32-ia32-msvc
2025-07-25 17:05:17.535 [info] 'WorkspaceManager[react]' Directory created: node_modules\asynckit
2025-07-25 17:05:17.536 [info] 'WorkspaceManager[react]' Directory created: node_modules\axios
2025-07-25 17:05:17.536 [info] 'WorkspaceManager[react]' Directory created: node_modules\call-bind-apply-helpers
2025-07-25 17:05:17.537 [info] 'WorkspaceManager[react]' Directory created: node_modules\combined-stream
2025-07-25 17:05:17.537 [info] 'WorkspaceManager[react]' Directory created: node_modules\delayed-stream
2025-07-25 17:05:17.537 [info] 'WorkspaceManager[react]' Directory created: node_modules\dunder-proto
2025-07-25 17:05:17.538 [info] 'WorkspaceManager[react]' Directory created: node_modules\es-define-property
2025-07-25 17:05:17.538 [info] 'WorkspaceManager[react]' Directory created: node_modules\es-errors
2025-07-25 17:05:17.539 [info] 'WorkspaceManager[react]' Directory created: node_modules\es-object-atoms
2025-07-25 17:05:17.539 [info] 'WorkspaceManager[react]' Directory created: node_modules\es-set-tostringtag
2025-07-25 17:05:17.540 [info] 'WorkspaceManager[react]' Directory created: node_modules\follow-redirects
2025-07-25 17:05:17.540 [info] 'WorkspaceManager[react]' Directory created: node_modules\form-data
2025-07-25 17:05:17.540 [info] 'WorkspaceManager[react]' Directory created: node_modules\fsevents
2025-07-25 17:05:17.541 [info] 'WorkspaceManager[react]' Directory created: node_modules\function-bind
2025-07-25 17:05:17.542 [info] 'WorkspaceManager[react]' Directory created: node_modules\get-intrinsic
2025-07-25 17:05:17.550 [info] 'WorkspaceManager[react]' Directory created: node_modules\get-proto
2025-07-25 17:05:17.551 [info] 'WorkspaceManager[react]' Directory created: node_modules\gopd
2025-07-25 17:05:17.552 [info] 'WorkspaceManager[react]' Directory created: node_modules\has-symbols
2025-07-25 17:05:17.553 [info] 'WorkspaceManager[react]' Directory created: node_modules\has-tostringtag
2025-07-25 17:05:17.554 [info] 'WorkspaceManager[react]' Directory created: node_modules\hasown
2025-07-25 17:05:17.555 [info] 'WorkspaceManager[react]' Directory created: node_modules\math-intrinsics
2025-07-25 17:05:17.555 [info] 'WorkspaceManager[react]' Directory created: node_modules\mime-db
2025-07-25 17:05:17.556 [info] 'WorkspaceManager[react]' Directory created: node_modules\mime-types
2025-07-25 17:05:17.557 [info] 'WorkspaceManager[react]' Directory created: node_modules\proxy-from-env
2025-07-25 17:05:17.989 [info] 'WorkspaceManager[react]' Directory created: node_modules\function-bind\test
2025-07-25 17:05:18.211 [info] 'WorkspaceManager[react]' Directory created: node_modules\es-object-atoms\test
2025-07-25 17:05:18.232 [info] 'WorkspaceManager[react]' Directory created: node_modules\get-intrinsic\test
2025-07-25 17:05:18.235 [info] 'WorkspaceManager[react]' Directory created: node_modules\has-symbols\test
2025-07-25 17:05:18.238 [info] 'WorkspaceManager[react]' Directory created: node_modules\has-tostringtag\test
2025-07-25 17:05:18.411 [info] 'WorkspaceManager[react]' Directory created: node_modules\combined-stream\lib
2025-07-25 17:05:18.461 [info] 'WorkspaceManager[react]' Directory created: node_modules\dunder-proto\test
2025-07-25 17:05:18.466 [info] 'WorkspaceManager[react]' Directory created: node_modules\es-define-property\test
2025-07-25 17:05:18.466 [info] 'WorkspaceManager[react]' Directory created: node_modules\es-errors\test
2025-07-25 17:05:18.467 [info] 'WorkspaceManager[react]' Directory created: node_modules\es-set-tostringtag\test
2025-07-25 17:05:18.468 [info] 'WorkspaceManager[react]' Directory created: node_modules\form-data\lib
2025-07-25 17:05:18.474 [info] 'WorkspaceManager[react]' Directory created: node_modules\get-proto\test
2025-07-25 17:05:18.475 [info] 'WorkspaceManager[react]' Directory created: node_modules\gopd\test
2025-07-25 17:05:18.476 [info] 'WorkspaceManager[react]' Directory created: node_modules\has-symbols\test\shams
2025-07-25 17:05:18.477 [info] 'WorkspaceManager[react]' Directory created: node_modules\has-tostringtag\test\shams
2025-07-25 17:05:18.478 [info] 'WorkspaceManager[react]' Directory created: node_modules\math-intrinsics\test
2025-07-25 17:05:18.604 [info] 'WorkspaceManager[react]' Directory created: node_modules\delayed-stream\lib
2025-07-25 17:05:18.959 [info] 'WorkspaceManager[react]' Directory created: node_modules\asynckit\lib
2025-07-25 17:05:19.019 [info] 'WorkspaceManager[react]' Directory created: node_modules\axios\dist
2025-07-25 17:05:19.023 [info] 'WorkspaceManager[react]' Directory created: node_modules\axios\dist\browser
2025-07-25 17:05:19.024 [info] 'WorkspaceManager[react]' Directory created: node_modules\call-bind-apply-helpers\test
2025-07-25 17:05:19.025 [info] 'WorkspaceManager[react]' Directory created: node_modules\es-define-property\.github
2025-07-25 17:05:19.028 [info] 'WorkspaceManager[react]' Directory created: node_modules\function-bind\.github
2025-07-25 17:05:19.030 [info] 'WorkspaceManager[react]' Directory created: node_modules\get-intrinsic\.github
2025-07-25 17:05:19.030 [info] 'WorkspaceManager[react]' Directory created: node_modules\hasown\.github
2025-07-25 17:05:19.031 [info] 'WorkspaceManager[react]' Directory created: node_modules\math-intrinsics\constants
2025-07-25 17:05:19.060 [info] 'WorkspaceManager[react]' Directory created: node_modules\gopd\.github
2025-07-25 17:05:19.686 [info] 'WorkspaceManager[react]' Directory created: node_modules\axios\dist\node
2025-07-25 17:05:19.743 [info] 'WorkspaceManager[react]' Directory created: node_modules\call-bind-apply-helpers\.github
2025-07-25 17:05:19.744 [info] 'WorkspaceManager[react]' Directory created: node_modules\dunder-proto\.github
2025-07-25 17:05:19.745 [info] 'WorkspaceManager[react]' Directory created: node_modules\es-errors\.github
2025-07-25 17:05:19.746 [info] 'WorkspaceManager[react]' Directory created: node_modules\es-object-atoms\.github
2025-07-25 17:05:19.747 [info] 'WorkspaceManager[react]' Directory created: node_modules\get-proto\.github
2025-07-25 17:05:19.748 [info] 'WorkspaceManager[react]' Directory created: node_modules\has-symbols\.github
2025-07-25 17:05:19.754 [info] 'WorkspaceManager[react]' Directory created: node_modules\has-tostringtag\.github
2025-07-25 17:05:20.094 [info] 'WorkspaceManager[react]' Directory created: node_modules\axios\lib
2025-07-25 17:05:20.119 [info] 'WorkspaceManager[react]' Directory created: node_modules\axios\lib\adapters
2025-07-25 17:05:20.123 [info] 'WorkspaceManager[react]' Directory created: node_modules\math-intrinsics\.github
2025-07-25 17:05:20.623 [info] 'WorkspaceManager[react]' Directory created: node_modules\axios\dist\esm
2025-07-25 17:05:20.651 [info] 'WorkspaceManager[react]' Directory created: node_modules\axios\lib\cancel
2025-07-25 17:05:20.658 [info] 'WorkspaceManager[react]' Directory created: node_modules\axios\lib\core
2025-07-25 17:05:20.660 [info] 'WorkspaceManager[react]' Directory created: node_modules\axios\lib\env
2025-07-25 17:05:20.662 [info] 'WorkspaceManager[react]' Directory created: node_modules\axios\lib\env\classes
2025-07-25 17:05:20.663 [info] 'WorkspaceManager[react]' Directory created: node_modules\axios\lib\helpers
2025-07-25 17:05:20.667 [info] 'WorkspaceManager[react]' Directory created: node_modules\axios\lib\platform
2025-07-25 17:05:20.671 [info] 'WorkspaceManager[react]' Directory created: node_modules\axios\lib\platform\browser
2025-07-25 17:05:20.672 [info] 'WorkspaceManager[react]' Directory created: node_modules\axios\lib\platform\browser\classes
2025-07-25 17:05:20.673 [info] 'WorkspaceManager[react]' Directory created: node_modules\axios\lib\platform\node
2025-07-25 17:05:20.674 [info] 'WorkspaceManager[react]' Directory created: node_modules\axios\lib\platform\node\classes
2025-07-25 17:05:21.111 [info] 'WorkspaceManager[react]' Directory created: node_modules\axios\lib\defaults
2025-07-25 17:05:21.148 [info] 'WorkspaceManager[react]' Directory created: node_modules\axios\lib\platform\common
2025-07-25 17:05:26.275 [info] 'WorkspaceManager[react]' Directory removed: node_modules\fsevents
2025-07-25 17:05:26.275 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\aix-ppc64
2025-07-25 17:05:26.275 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\linux-arm
2025-07-25 17:05:26.275 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\linux-x64
2025-07-25 17:05:26.275 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\sunos-x64
2025-07-25 17:05:26.275 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\darwin-x64
2025-07-25 17:05:26.275 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\linux-ia32
2025-07-25 17:05:26.275 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\netbsd-x64
2025-07-25 17:05:26.276 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\win32-ia32
2025-07-25 17:05:26.276 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\android-arm
2025-07-25 17:05:26.276 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\android-x64
2025-07-25 17:05:26.276 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\freebsd-x64
2025-07-25 17:05:26.276 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\linux-arm64
2025-07-25 17:05:26.276 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\linux-ppc64
2025-07-25 17:05:26.276 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\linux-s390x
2025-07-25 17:05:26.276 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\openbsd-x64
2025-07-25 17:05:26.276 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\win32-arm64
2025-07-25 17:05:26.276 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\darwin-arm64
2025-07-25 17:05:26.276 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\netbsd-arm64
2025-07-25 17:05:26.276 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\android-arm64
2025-07-25 17:05:26.276 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\freebsd-arm64
2025-07-25 17:05:26.277 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\linux-loong64
2025-07-25 17:05:26.277 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\linux-riscv64
2025-07-25 17:05:26.277 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\openbsd-arm64
2025-07-25 17:05:26.277 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\linux-mips64el
2025-07-25 17:05:26.277 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-darwin-x64
2025-07-25 17:05:26.277 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@esbuild\openharmony-arm64
2025-07-25 17:05:26.277 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-freebsd-x64
2025-07-25 17:05:26.277 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-darwin-arm64
2025-07-25 17:05:26.277 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-android-arm64
2025-07-25 17:05:26.277 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-freebsd-arm64
2025-07-25 17:05:26.277 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-linux-x64-gnu
2025-07-25 17:05:26.277 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-linux-x64-musl
2025-07-25 17:05:26.277 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-linux-arm64-gnu
2025-07-25 17:05:26.277 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-linux-s390x-gnu
2025-07-25 17:05:26.277 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-win32-ia32-msvc
2025-07-25 17:05:26.277 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-android-arm-eabi
2025-07-25 17:05:26.277 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-linux-arm64-musl
2025-07-25 17:05:26.277 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-win32-arm64-msvc
2025-07-25 17:05:26.277 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-linux-riscv64-gnu
2025-07-25 17:05:26.277 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-linux-riscv64-musl
2025-07-25 17:05:26.278 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-linux-arm-gnueabihf
2025-07-25 17:05:26.278 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-linux-arm-musleabihf
2025-07-25 17:05:26.278 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-linux-loongarch64-gnu
2025-07-25 17:05:26.278 [info] 'WorkspaceManager[react]' Directory removed: node_modules\@rollup\rollup-linux-powerpc64le-gnu
2025-07-25 17:05:26.307 [info] 'StallDetector' Event loop delay: Timer(100 msec) ran 2603 msec late.
2025-07-25 17:05:42.386 [info] 'WorkspaceManager[react]' Directory created: node_modules\.vite-temp
2025-07-25 17:05:42.893 [info] 'WorkspaceManager[react]' Directory created: node_modules\.vite
2025-07-25 17:05:42.905 [info] 'WorkspaceManager[react]' Directory created: node_modules\.vite\deps_temp_883627b7
2025-07-25 17:05:44.184 [info] 'WorkspaceManager[react]' Directory created: node_modules\.vite\deps
2025-07-25 17:05:44.184 [info] 'WorkspaceManager[react]' Directory removed: node_modules\.vite\deps_temp_883627b7
2025-07-25 17:06:52.153 [error] 'AugmentExtension' API request 801863e7-2215-4ef9-bf6a-893ce881cb63 to https://d1.api.augmentcode.com/record-session-events response 403: Forbidden
2025-07-25 17:06:52.157 [error] 'AugmentExtension' API request bbe2424a-6f0d-4727-933d-92c4f28cd844 to https://d1.api.augmentcode.com/record-request-events response 403: Forbidden
2025-07-25 17:06:52.675 [error] 'AugmentExtension' API request 81b073e0-ad47-4874-9625-f844c6d306fa to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:06:52.676 [error] 'AugmentExtension' Dropping error report "record-session-events call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:06:52.676 [error] 'AgentSessionEventReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.logAgentSessionEvent (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:34291)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:06:52.676 [error] 'AgentSessionEventReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.logAgentSessionEvent (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:34291)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:06:52.775 [error] 'AugmentExtension' API request 2b18087d-2c2f-4b28-b616-9a8b82308a30 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:06:52.775 [error] 'AugmentExtension' Dropping error report "record-request-events call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:06:52.775 [error] 'AgentRequestEventReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.logAgentRequestEvent (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:35106)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:06:52.775 [error] 'AgentRequestEventReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.logAgentRequestEvent (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:35106)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:07:02.122 [error] 'AugmentExtension' API request 85fb7caf-0e1f-447c-ab1f-21992a0727db to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:07:02.162 [error] 'AugmentExtension' API request ce03fe11-0f32-483d-96d5-789cad10625c to https://d1.api.augmentcode.com/record-session-events response 403: Forbidden
2025-07-25 17:07:02.376 [error] 'AugmentExtension' API request bbe2424a-6f0d-4727-933d-92c4f28cd844 to https://d1.api.augmentcode.com/record-request-events response 403: Forbidden
2025-07-25 17:07:02.630 [error] 'AugmentExtension' API request c43da1ce-2b1c-4332-9af6-2833f7e16c91 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:07:02.631 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:07:02.631 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:07:02.631 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:07:02.714 [error] 'AugmentExtension' API request 87ea0367-d07f-43f7-a841-4d0cd989b922 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:07:02.715 [error] 'AugmentExtension' Dropping error report "record-session-events call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:07:02.715 [error] 'AgentSessionEventReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.logAgentSessionEvent (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:34291)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:07:02.715 [error] 'AgentSessionEventReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.logAgentSessionEvent (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:34291)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:07:02.888 [error] 'AugmentExtension' API request ccbb9903-8fcf-40cd-a1ff-ec75679c9045 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:07:02.889 [error] 'AugmentExtension' Dropping error report "record-request-events call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:07:02.889 [error] 'ToolUseRequestEventReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.logToolUseRequestEvent (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:35793)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:07:02.890 [error] 'ToolUseRequestEventReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.logToolUseRequestEvent (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:35793)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:07:12.117 [error] 'AugmentExtension' API request 39e4751e-3dfc-4145-977d-f8639c13761d to https://d1.api.augmentcode.com/record-request-events response 403: Forbidden
2025-07-25 17:07:12.629 [error] 'AugmentExtension' API request 9f86df0f-2266-4de6-b388-0604e6c403a6 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:07:12.630 [error] 'AugmentExtension' Dropping error report "record-request-events call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:07:12.630 [error] 'AgentRequestEventReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.logAgentRequestEvent (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:35106)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:07:12.631 [error] 'AgentRequestEventReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.logAgentRequestEvent (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:35106)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:07:13.065 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1032.1772,"timestamp":"2025-07-25T16:07:13.049Z"}]
2025-07-25 17:07:32.152 [error] 'AugmentExtension' API request 5479256e-6432-4ef6-b9e3-a8eea4f9233a to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:07:32.675 [error] 'AugmentExtension' API request 5857eb29-24f0-4d01-8fe6-95765c4c32f2 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:07:32.675 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:07:32.676 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:07:32.676 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:07:42.101 [error] 'AugmentExtension' API request 813fecf8-af65-459e-b3ef-4e11c114607c to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:07:42.621 [error] 'AugmentExtension' API request 93c3096c-617a-478c-9261-a417024aa48e to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:07:42.621 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:07:42.621 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:07:42.622 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:07:52.155 [error] 'AugmentExtension' API request a9df7693-4f74-498a-b4ab-2a6b9f8014fb to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:07:52.678 [error] 'AugmentExtension' API request 2f3da6d2-6f76-45e8-91b3-80de614345bf to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:07:52.679 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:07:52.679 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:07:52.679 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:08:13.952 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1932.4256,"timestamp":"2025-07-25T16:08:13.951Z"}]
2025-07-25 17:08:22.189 [error] 'AugmentExtension' API request d5e1f20b-1d21-49bd-80e1-6f249181629e to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:08:22.712 [error] 'AugmentExtension' API request d3aafc7b-4487-4a08-a198-a23a88922e05 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:08:22.713 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:08:22.714 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:08:22.714 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:09:22.381 [error] 'AugmentExtension' API request 65d71922-c1cc-4451-84e6-14d62e467388 to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:09:22.899 [error] 'AugmentExtension' API request b3ec9462-1e91-4c9b-ab47-a0991c1b0cf0 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:09:22.899 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:09:22.899 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:09:22.900 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:09:32.261 [error] 'AugmentExtension' API request 5904346c-6c96-4597-a624-ff43c99ab661 to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:09:32.785 [error] 'AugmentExtension' API request ea0afe09-a527-4e79-82f0-3b2d288c0b5c to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:09:32.785 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:09:32.786 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:09:32.786 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:10:02.302 [error] 'AugmentExtension' API request 56729aa8-831d-4e43-b4c2-08fae0132b6e to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:10:02.826 [error] 'AugmentExtension' API request d606fa2a-d3a6-4896-a0bc-b3437725bcd9 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:10:02.826 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:10:02.826 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:10:02.827 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:10:13.400 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1286.1703,"timestamp":"2025-07-25T16:10:13.301Z"}]
2025-07-25 17:10:23.509 [error] 'AugmentExtension' API request e77e1039-ae0e-4bfb-a13b-c9e1962ee7ac to https://d1.api.augmentcode.com/record-session-events response 403: Forbidden
2025-07-25 17:10:23.830 [error] 'AugmentExtension' API request 8ca8a23e-8a70-4dea-8af1-57ae071a7509 to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:10:24.059 [error] 'AugmentExtension' API request 7caa7aee-f8ee-4bf5-9852-89bc837e9d7a to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:10:24.059 [error] 'AugmentExtension' Dropping error report "record-session-events call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:10:24.059 [error] 'NextEditSessionEventReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.logNextEditSessionEvent (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:33584)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:10:24.059 [error] 'NextEditSessionEventReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.logNextEditSessionEvent (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:33584)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:10:24.352 [error] 'AugmentExtension' API request 69883167-718d-42a4-bf47-5d6f2441c824 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:10:24.352 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:10:24.352 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:10:24.352 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:10:32.540 [error] 'AugmentExtension' API request 225c3e66-2a79-4cd7-b49e-c8968a2f2ad0 to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:10:33.055 [error] 'AugmentExtension' API request 06bbd7cf-8e28-4474-935f-e1a546fd2152 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:10:33.055 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:10:33.056 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:10:33.056 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:10:42.243 [error] 'AugmentExtension' API request 19ff6712-b41d-490d-a000-e559fbb7b734 to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:10:42.753 [error] 'AugmentExtension' API request 027a1c3c-d984-4875-bf00-77a929b0e524 to https://d1.api.augmentcode.com/report-feature-vector response 403: Forbidden
2025-07-25 17:10:42.856 [error] 'AugmentExtension' API request 6dc2bf25-cb90-425a-ae3c-fbe835b4dea7 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:10:42.857 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:10:42.857 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:10:42.857 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:10:43.294 [error] 'AugmentExtension' API request 230b027a-67f6-4ded-bd1b-d1bc163ee85a to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:10:43.295 [error] 'AugmentExtension' Dropping error report "report-feature-vector call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:10:43.295 [error] 'FeatureVectorReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.logFeatureVector (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:33380)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:10:43.296 [error] 'FeatureVectorReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.logFeatureVector (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:33380)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:10:52.349 [error] 'AugmentExtension' API request 8b8df7ff-bef6-48c7-93fc-11fe5acccfcf to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:10:52.867 [error] 'AugmentExtension' API request 630a0aee-364b-42c6-9105-3f650456f462 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:10:52.868 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:10:52.868 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:10:52.869 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:11:02.381 [error] 'AugmentExtension' API request b9885cc2-b281-471f-8435-3e0ec6b573ed to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:11:02.899 [error] 'AugmentExtension' API request 95a2e6cd-e943-491c-85cc-c507d812dfed to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:11:02.900 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:11:02.900 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:11:02.901 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:11:32.581 [error] 'AugmentExtension' API request f1d127ad-0461-4ddf-89f3-64934f19802e to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:11:33.101 [error] 'AugmentExtension' API request 4ef39d00-e414-4152-af50-d8f30369feb5 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:11:33.101 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:11:33.102 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:11:33.102 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:11:42.376 [error] 'AugmentExtension' API request 933d2277-5bf0-4322-9624-2317ae816e78 to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:11:42.892 [error] 'AugmentExtension' API request daad4fe9-edbb-4b27-ace7-81134e606451 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:11:42.892 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:11:42.893 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:11:42.893 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:11:52.820 [error] 'AugmentExtension' API request 9b8e51d4-4ed3-4251-99a6-796e97e81878 to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:11:53.335 [error] 'AugmentExtension' API request acb4d73f-03fe-4617-9d24-3b0d47a0c869 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:11:53.336 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:11:53.336 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:11:53.336 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:12:52.445 [error] 'AugmentExtension' API request 0f2f5563-e2d4-480b-9dfb-8f68eb7a743f to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:12:52.962 [error] 'AugmentExtension' API request 10cddd3a-8658-47ce-9581-a3a7ee983ffa to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:12:52.963 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:12:52.963 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:12:52.964 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:13:02.499 [error] 'AugmentExtension' API request af7ed387-6073-44b2-a4ee-74bdd2b09345 to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:13:03.020 [error] 'AugmentExtension' API request 6efd7026-7391-43c3-9d97-30538a85d0ff to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:13:03.020 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:13:03.021 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:13:03.021 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:13:12.423 [error] 'AugmentExtension' API request 2214297c-9662-49ed-9edf-db7944a6c04c to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:13:12.941 [error] 'AugmentExtension' API request 162b4343-0f6d-4111-a0e6-80ef27cbdc89 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:13:12.941 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:13:12.942 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:13:12.942 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:13:22.459 [error] 'AugmentExtension' API request 151f0632-cdd2-4658-bfd9-5d3f81744745 to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:13:22.981 [error] 'AugmentExtension' API request 50fd4718-f61a-436d-b1f1-ae33b2770a96 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:13:22.981 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:13:22.982 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:13:22.982 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:13:52.539 [error] 'AugmentExtension' API request 0ce05866-1db5-423c-aa0e-dbb1607da750 to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:13:53.056 [error] 'AugmentExtension' API request 6309814f-8f83-47ee-aa19-506d543e7bb5 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:13:53.056 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:13:53.057 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:13:53.057 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:14:12.487 [error] 'AugmentExtension' API request 2c6ca178-8ea1-44ef-8182-613ca609293b to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:14:13.010 [error] 'AugmentExtension' API request a3c45ae1-8280-43b2-9841-4a86bb8a3231 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:14:13.010 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:14:13.011 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:14:13.011 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:14:22.421 [error] 'AugmentExtension' API request d45ca6ef-68df-49e3-a70b-c35a564f8ecc to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:14:22.943 [error] 'AugmentExtension' API request 3b73c3df-fde1-4836-bcf6-147a96d95ae2 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:14:22.943 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:14:22.944 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:14:22.944 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:14:32.547 [error] 'AugmentExtension' API request 67077fed-cd04-481f-993c-38157ed2e460 to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:14:33.093 [error] 'AugmentExtension' API request fc64cc4f-f2e4-442a-96f9-be6b65c4b8fb to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:14:33.094 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:14:33.097 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:14:33.098 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:14:52.440 [error] 'AugmentExtension' API request 9ad4a3a6-6eb5-43cb-b36f-b403a48205d1 to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:14:52.953 [error] 'AugmentExtension' API request eb1f2355-86cb-4d75-89dc-87a83a3bd5b2 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:14:52.953 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:14:52.953 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:14:52.954 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:15:22.481 [error] 'AugmentExtension' API request 165d0f19-288a-477d-8e48-8f691fb6847a to https://d1.api.augmentcode.com/record-session-events response 403: Forbidden
2025-07-25 17:15:23.001 [error] 'AugmentExtension' API request 90a6130d-20d9-44e5-8c14-c47f06a605fb to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:15:23.001 [error] 'AugmentExtension' Dropping error report "record-session-events call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:15:23.001 [error] 'NextEditSessionEventReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.logNextEditSessionEvent (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:33584)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:15:23.002 [error] 'NextEditSessionEventReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.logNextEditSessionEvent (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:33584)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:16:22.566 [error] 'AugmentExtension' API request 09b4f2bc-7e94-49f9-be2d-6d53aa548247 to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:16:23.093 [error] 'AugmentExtension' API request ce5d6a39-3c68-4597-8569-87b82bbd7667 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:16:23.094 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:16:23.094 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:16:23.095 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:16:32.570 [error] 'AugmentExtension' API request b4aed796-cf29-4e36-9d5f-eccfab1b1e7b to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:16:33.092 [error] 'AugmentExtension' API request 0f2a7314-fc43-428c-9081-5537d5085bd4 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:16:33.092 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:16:33.093 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:16:33.093 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:16:52.563 [error] 'AugmentExtension' API request f25afe8a-faad-4c1c-bd9b-a73801c1ac0f to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:16:53.083 [error] 'AugmentExtension' API request d616ad8d-ca15-4ee0-8337-d7e43d3c8f03 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:16:53.083 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:16:53.084 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:16:53.084 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:17:02.599 [error] 'AugmentExtension' API request ca0f05f0-70cd-45c3-b969-193135034a37 to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:17:03.121 [error] 'AugmentExtension' API request 65fb0a54-3085-4a6c-8878-3ce251b88e73 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:17:03.121 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:17:03.122 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:17:03.122 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:17:12.645 [error] 'AugmentExtension' API request 5cdedb10-e5e3-4fed-991d-1cf36362a200 to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:17:13.167 [error] 'AugmentExtension' API request 0d2c08ec-8d63-447f-aad8-4931e0da14c8 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:17:13.167 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:17:13.168 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:17:13.168 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:17:22.642 [error] 'AugmentExtension' API request 52e75f7a-f57c-4009-a7cf-a843d51d80a4 to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:17:23.160 [error] 'AugmentExtension' API request 1a5300d0-150a-4c95-ae11-12fd1fed9717 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:17:23.160 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:17:23.161 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:17:23.161 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:17:32.603 [error] 'AugmentExtension' API request b7e6093c-aa26-476e-82ae-c9e214506e3a to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:17:33.120 [error] 'AugmentExtension' API request 4eacd3a0-4933-4d66-ac54-28d98d32d2d3 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:17:33.121 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:17:33.121 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:17:33.122 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:17:42.593 [error] 'AugmentExtension' API request 54365391-2af7-4090-8c26-440a6279f78d to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:17:43.108 [error] 'AugmentExtension' API request 5d989467-2938-4770-926b-00e53a972cec to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:17:43.109 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:17:43.109 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:17:43.109 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:17:52.696 [error] 'AugmentExtension' API request bcd0fcef-8989-4cef-a71c-d0d41465c03f to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:17:53.226 [error] 'AugmentExtension' API request 032cdab0-29be-4480-9bec-9f7a564886d2 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:17:53.227 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:17:53.227 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:17:53.228 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:18:12.697 [error] 'AugmentExtension' API request 95f626ae-1507-4e21-9de5-e3f92572fc7c to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:18:13.215 [error] 'AugmentExtension' API request 370e7d04-235d-4e94-bddb-8f1da3b52480 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:18:13.216 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:18:13.216 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:18:13.217 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:18:22.625 [error] 'AugmentExtension' API request 0a021680-9123-4b33-9092-efa060bd5813 to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:18:23.141 [error] 'AugmentExtension' API request cd72cd1d-0fe2-4403-b939-ac3daaf6981b to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:18:23.142 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:18:23.142 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:18:23.143 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:18:43.314 [info] 'StallDetector' Recent work: [{"name":"get-subscription-info","durationMs":1249.1477,"timestamp":"2025-07-25T16:18:43.271Z"}]
2025-07-25 17:19:22.699 [error] 'AugmentExtension' API request 7602a690-a86c-4952-abef-17c8d49a4752 to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:19:23.220 [error] 'AugmentExtension' API request 30a78cf4-7081-4e9d-89bf-bbfdad67dbfc to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:19:23.220 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:19:23.221 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:19:23.221 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:19:32.717 [error] 'AugmentExtension' API request 66160c03-8d38-4e41-9644-d0c51ce70c97 to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:19:33.236 [error] 'AugmentExtension' API request f36a7be3-85cf-4879-94b9-ce322db3fdbc to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:19:33.237 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:19:33.237 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:19:33.238 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:19:42.800 [error] 'AugmentExtension' API request e84f40db-7738-4daf-bba9-ee86f9279cb1 to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:19:43.318 [error] 'AugmentExtension' API request 372f7c21-1ed6-48e4-98ca-63ae6708a268 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:19:43.319 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:19:43.319 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:19:43.319 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:20:02.774 [error] 'AugmentExtension' API request 88c73ea5-ce58-4dfb-a10a-3f114e3c4f3c to https://d1.api.augmentcode.com/client-metrics response 403: Forbidden
2025-07-25 17:20:03.291 [error] 'AugmentExtension' API request 99257394-da55-4600-8c46-380fe31b15b5 to https://d1.api.augmentcode.com/report-error failed: This operation was aborted
2025-07-25 17:20:03.291 [error] 'AugmentExtension' Dropping error report "client-metrics call failed with APIStatus permissionDenied" due to error: This operation was aborted
2025-07-25 17:20:03.292 [error] 'ClientMetricsReporter' Error uploading metrics: Error: HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:20:03.292 [error] 'ClientMetricsReporter' Critical error in background metrics upload (will continue): HTTP error: 403 Forbidden Error: HTTP error: 403 Forbidden
    at e.fromResponse (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:399:22158)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:13190)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at VH.callApi (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:56799)
    at VH.clientMetrics (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:641:52780)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7983
    at wo (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:406:6285)
    at e._doUpload (c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7882)
    at c:\Users\<USER>\.vscode\extensions\augment.vscode-augment-0.509.1\out\extension.js:408:7205
2025-07-25 17:20:10.990 [info] 'PathMap' Closed source folder c:\Users\<USER>\Desktop\my_app\react with id 100
2025-07-25 17:20:10.990 [info] 'OpenFileManager' Closed source folder 100
