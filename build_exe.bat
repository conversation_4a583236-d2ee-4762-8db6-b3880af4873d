@echo off
echo Building Augment Cleaner Executable...
echo.

REM Clean previous builds
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist


REM Build AugmentCleanerV2 as a standalone Windows EXE
python -m pip install pyinstaller
python -m PyInstaller --onefile --clean --name "AugmentCleanerV2" --console augment_cleaner_v2.py

echo.
echo Build complete!
echo Executable location: dist\AugmentCleaner.exe
echo.
pause
