import os
import sqlite3
import shutil
import platform
import sys
import subprocess

def find_vscode_config_dirs():
    """Find all VSCode-based IDE configuration directories on Windows"""
    os_type = platform.system()
    if os_type != "Windows":
        print("This script is designed for Windows only.")
        sys.exit(1)

    # Common VSCode-based IDEs on Windows
    vscode_dirs = []
    appdata = os.path.expandvars(r"%APPDATA%")

    # Standard VSCode
    vscode_path = os.path.join(appdata, "Code", "User", "globalStorage")
    if os.path.exists(vscode_path):
        vscode_dirs.append(("VSCode", vscode_path))

    # VSCode Insiders
    vscode_insiders_path = os.path.join(appdata, "Code - Insiders", "User", "globalStorage")
    if os.path.exists(vscode_insiders_path):
        vscode_dirs.append(("VSCode Insiders", vscode_insiders_path))

    # Cursor (VSCode-based)
    cursor_path = os.path.join(appdata, "Cursor", "User", "globalStorage")
    if os.path.exists(cursor_path):
        vscode_dirs.append(("Cursor", cursor_path))

    return vscode_dirs

def check_augment_extension():
    """Check if Augment extension is installed in VSCode"""
    vscode_dirs = find_vscode_config_dirs()
    extensions_found = []

    # Known Augment extension identifiers
    augment_identifiers = [
        "augment",
        "augment-code",
        "augmentcode",
        "augment.augment-code",
        "augmentcode.augment-code"
    ]

    for ide_name, config_dir in vscode_dirs:
        # Multiple possible extension directory locations
        user_dir = os.path.dirname(config_dir)  # User directory

        possible_extension_dirs = [
            # Standard location: %APPDATA%\Code\User\extensions
            os.path.join(user_dir, "extensions"),
            # Alternative location: %USERPROFILE%\.vscode\extensions
            os.path.expanduser("~/.vscode/extensions"),
            # Global extensions: %APPDATA%\Code\extensions
            os.path.join(os.path.dirname(user_dir), "extensions"),
            # Insiders location
            os.path.expandvars(r"%APPDATA%\Code - Insiders\User\extensions"),
            # Cursor location
            os.path.expandvars(r"%APPDATA%\Cursor\User\extensions")
        ]

        for extensions_dir in possible_extension_dirs:
            if not os.path.exists(extensions_dir):
                continue

            print(f"   🔍 Checking extensions in: {extensions_dir}")

            try:
                items = os.listdir(extensions_dir)
                print(f"   📁 Found {len(items)} total extensions")

                for item in items:
                    # Check for any Augment-related names
                    item_lower = item.lower()
                    for identifier in augment_identifiers:
                        if identifier in item_lower:
                            extensions_found.append((ide_name, item, extensions_dir))
                            print(f"   📦 Found Augment: {item}")
                            break

                # Show a few example extensions for debugging
                if items:
                    sample_extensions = items[:3]
                    print(f"   📋 Sample extensions: {', '.join(sample_extensions)}...")

                # If we found extensions in this directory, we can break
                if extensions_found:
                    break

            except PermissionError:
                print(f"   ❌ Permission denied accessing extensions directory")
            except Exception as e:
                print(f"   ❌ Error reading extensions: {str(e)}")

    return extensions_found

def check_augment_system_wide():
    """Check for Augment installation system-wide"""
    print("   🌐 Checking system-wide Augment installation...")

    # Check common installation paths
    common_paths = [
        os.path.expandvars(r"%LOCALAPPDATA%\Programs\Augment"),
        os.path.expandvars(r"%PROGRAMFILES%\Augment"),
        os.path.expandvars(r"%PROGRAMFILES(X86)%\Augment"),
        os.path.expandvars(r"%APPDATA%\Augment"),
        os.path.expanduser("~/.augment"),
    ]

    found_paths = []
    for path in common_paths:
        if os.path.exists(path):
            found_paths.append(path)
            print(f"   📁 Found Augment directory: {path}")

    # Check for Augment processes
    try:
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq *augment*'],
                              capture_output=True, text=True, shell=True)
        if result.returncode == 0 and 'augment' in result.stdout.lower():
            print(f"   🔄 Found Augment process running")
    except Exception as e:
        print(f"   ❌ Error checking processes: {str(e)}")

    return found_paths

def check_augment_registry():
    """Check Windows Registry for Augment entries"""
    print("   🗂️  Checking Windows Registry...")

    try:
        import winreg
        registry_paths = [
            (winreg.HKEY_CURRENT_USER, r"Software\Microsoft\Windows\CurrentVersion\Uninstall"),
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall"),
            (winreg.HKEY_CURRENT_USER, r"Software"),
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE"),
        ]

        found_entries = []
        for hkey, path in registry_paths:
            try:
                with winreg.OpenKey(hkey, path) as key:
                    i = 0
                    while True:
                        try:
                            subkey_name = winreg.EnumKey(key, i)
                            if "augment" in subkey_name.lower():
                                found_entries.append(f"{path}\\{subkey_name}")
                                print(f"   📝 Found registry entry: {subkey_name}")
                            i += 1
                        except WindowsError:
                            break
            except Exception:
                continue

        return found_entries

    except ImportError:
        print("   ❌ Registry check not available (winreg not found)")
        return []
    except Exception as e:
        print(f"   ❌ Registry check failed: {str(e)}")
        return []

def check_augment_temp_files():
    """Check for Augment temporary files and cache"""
    print("   🗃️  Checking temporary files and cache...")

    temp_locations = [
        os.path.expandvars(r"%TEMP%"),
        os.path.expandvars(r"%LOCALAPPDATA%\Temp"),
        os.path.expandvars(r"%APPDATA%\Code\User\workspaceStorage"),
        os.path.expandvars(r"%APPDATA%\Code\CachedExtensions"),
        os.path.expandvars(r"%APPDATA%\Code\logs"),
    ]

    found_files = []
    for location in temp_locations:
        if not os.path.exists(location):
            continue

        try:
            for root, dirs, files in os.walk(location):
                # Check directories
                for dir_name in dirs:
                    if "augment" in dir_name.lower():
                        found_files.append(os.path.join(root, dir_name))
                        print(f"   📁 Found temp directory: {dir_name}")

                # Check files
                for file_name in files:
                    if "augment" in file_name.lower():
                        found_files.append(os.path.join(root, file_name))
                        print(f"   📄 Found temp file: {file_name}")

                # Don't go too deep to avoid performance issues
                if len(root.split(os.sep)) - len(location.split(os.sep)) > 2:
                    dirs.clear()

        except (PermissionError, OSError):
            continue

    return found_files

def check_augment_environment():
    """Check environment variables for Augment traces"""
    print("   🌍 Checking environment variables...")

    found_vars = []
    for key, value in os.environ.items():
        if "augment" in key.lower() or "augment" in value.lower():
            found_vars.append((key, value))
            print(f"   🔧 Found env var: {key} = {value[:50]}...")

    return found_vars

def check_augment_network_traces():
    """Check for Augment network configuration and API traces"""
    print("   🌐 Checking network traces and API configurations...")

    # Check hosts file for Augment domains
    hosts_file = r"C:\Windows\System32\drivers\etc\hosts"
    found_traces = []

    try:
        if os.path.exists(hosts_file):
            with open(hosts_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                if "augment" in content.lower():
                    found_traces.append("hosts_file")
                    print("   🌐 Found Augment entries in hosts file")
    except Exception:
        pass

    # Check for Augment API tokens in common locations
    token_locations = [
        os.path.expandvars(r"%APPDATA%\Code\User\globalStorage\state.vscdb"),
        os.path.expanduser("~/.augment"),
        os.path.expanduser("~/.config/augment"),
    ]

    for location in token_locations:
        if os.path.exists(location):
            try:
                if os.path.isfile(location):
                    with open(location, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        if any(keyword in content.lower() for keyword in ['token', 'api', 'auth', 'session']):
                            found_traces.append(location)
                            print(f"   🔑 Found potential API traces in: {os.path.basename(location)}")
            except Exception:
                continue

    return found_traces

def check_vscode_settings():
    """Check VSCode settings for Augment configuration"""
    vscode_dirs = find_vscode_config_dirs()
    settings_found = []

    for ide_name, config_dir in vscode_dirs:
        user_dir = os.path.dirname(config_dir)
        settings_file = os.path.join(user_dir, "settings.json")

        if os.path.exists(settings_file):
            try:
                with open(settings_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if "augment" in content.lower():
                        settings_found.append((ide_name, settings_file))
                        print(f"   ⚙️  {ide_name}: Found Augment settings in settings.json")
            except Exception as e:
                print(f"   ❌ Error reading settings.json: {str(e)}")

    return settings_found

def check_extension_host_logs():
    """Check extension host logs for Augment activity"""
    vscode_dirs = find_vscode_config_dirs()

    for ide_name, config_dir in vscode_dirs:
        # Check logs directory
        code_dir = os.path.dirname(os.path.dirname(config_dir))
        logs_dir = os.path.join(code_dir, "logs")

        if os.path.exists(logs_dir):
            try:
                # Look for recent log directories
                log_dirs = [d for d in os.listdir(logs_dir) if os.path.isdir(os.path.join(logs_dir, d))]
                if log_dirs:
                    # Check the most recent log directory
                    recent_log = max(log_dirs)
                    exthost_log = os.path.join(logs_dir, recent_log, "exthost1", "output.log")

                    if os.path.exists(exthost_log):
                        with open(exthost_log, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()
                            if "augment" in content.lower():
                                print(f"   📝 {ide_name}: Found Augment activity in extension logs")
                                return True
            except Exception as e:
                print(f"   ❌ Error checking logs: {str(e)}")

    return False

def scan_augment_entries():
    """Scan for Augment entries without removing them"""
    print("🔍 Scanning for Augment Code installations and database entries...")
    print("=" * 60)

    vscode_dirs = find_vscode_config_dirs()

    if not vscode_dirs:
        print("❌ No VSCode-based IDEs found on this system.")
        return None, 0

    print(f"✅ Found {len(vscode_dirs)} VSCode-based IDE(s):")
    for ide_name, config_dir in vscode_dirs:
        print(f"   • {ide_name}")

    print()

    # Check for installed extensions
    print("🔌 Checking for Augment Extensions...")
    extensions = check_augment_extension()
    if extensions:
        print("   � Augment Extensions Found:")
        for ide_name, ext_name, ext_path in extensions:
            print(f"   • {ide_name}: {ext_name}")
            print(f"     📁 Location: {ext_path}")
    else:
        print("   ✅ No Augment extensions found in extensions directory")

    # Check VSCode settings
    print("\n⚙️  Checking VSCode Settings...")
    settings = check_vscode_settings()
    if not settings:
        print("   ✅ No Augment settings found")

    # Check extension logs
    print("\n📝 Checking Extension Logs...")
    logs_found = check_extension_host_logs()
    if not logs_found:
        print("   ✅ No recent Augment activity in logs")

    # Check system-wide installation
    print("\n🌐 Checking System-wide Installation...")
    system_paths = check_augment_system_wide()
    if not system_paths:
        print("   ✅ No system-wide Augment installation found")

    # Check Windows Registry
    print("\n🗂️  Checking Windows Registry...")
    registry_entries = check_augment_registry()
    if not registry_entries:
        print("   ✅ No Augment registry entries found")

    # Check temporary files and cache
    print("\n🗃️  Checking Temporary Files...")
    temp_files = check_augment_temp_files()
    if not temp_files:
        print("   ✅ No Augment temporary files found")

    # Check environment variables
    print("\n🌍 Checking Environment Variables...")
    env_vars = check_augment_environment()
    if not env_vars:
        print("   ✅ No Augment environment variables found")

    # Check network traces
    print("\n🌐 Checking Network Traces...")
    network_traces = check_augment_network_traces()
    if not network_traces:
        print("   ✅ No Augment network traces found")

    print()

    # Check database entries
    total_entries = 0
    entries_by_ide = {}

    print("🗄️  Checking database entries...")

    for ide_name, config_dir in vscode_dirs:
        db_path = os.path.join(config_dir, "state.vscdb")

        if not os.path.exists(db_path):
            print(f"   • {ide_name}: No state.vscdb found")
            continue

        try:
            conn = sqlite3.connect(db_path)
            cur = conn.cursor()

            # Search for Augment-related keys with multiple patterns
            augment_patterns = ['%augment%', '%augmentcode%', '%augment-code%', '%augment.%']
            all_keys = []

            for pattern in augment_patterns:
                cur.execute("SELECT key FROM ItemTable WHERE LOWER(key) LIKE LOWER(?)", (pattern,))
                pattern_keys = cur.fetchall()
                for key in pattern_keys:
                    if key not in all_keys:  # Avoid duplicates
                        all_keys.append(key)

            keys = all_keys

            if keys:
                entries_by_ide[ide_name] = keys
                total_entries += len(keys)
                print(f"   • {ide_name}: Found {len(keys)} Augment entries")

                # Show first few entries as examples
                for key in keys[:3]:
                    print(f"     - {key[0]}")
                if len(keys) > 3:
                    print(f"     - ... and {len(keys) - 3} more entries")
            else:
                print(f"   • {ide_name}: No Augment entries found")

            conn.close()

        except Exception as e:
            print(f"   • {ide_name}: Error reading database - {str(e)}")

    return {
        'entries_by_ide': entries_by_ide,
        'total_entries': total_entries,
        'extensions': extensions,
        'settings': settings,
        'temp_files': temp_files,
        'registry_entries': registry_entries,
        'env_vars': env_vars,
        'network_traces': network_traces
    }

def clean_augment_entries(entries_by_ide):
    """Clean the Augment entries from VSCode databases"""
    print("\n🧹 Starting database cleanup...")

    cleaned_count = 0

    for ide_name, keys in entries_by_ide.items():
        config_dir = None
        for name, path in find_vscode_config_dirs():
            if name == ide_name:
                config_dir = path
                break

        if not config_dir:
            continue

        db_path = os.path.join(config_dir, "state.vscdb")

        try:
            # Create backup
            backup_path = db_path + ".bak"
            shutil.copy2(db_path, backup_path)
            print(f"   ✅ Created backup: {os.path.basename(backup_path)}")

            conn = sqlite3.connect(db_path)
            cur = conn.cursor()

            # Remove the entries
            for key in keys:
                cur.execute("DELETE FROM ItemTable WHERE key = ?", (key[0],))

            conn.commit()
            conn.close()

            print(f"   ✅ {ide_name}: Successfully removed {len(keys)} database entries")
            cleaned_count += len(keys)

        except Exception as e:
            print(f"   ❌ {ide_name}: Error during cleanup - {str(e)}")

    return cleaned_count

def clean_augment_extensions(extensions_found):
    """Remove Augment extension files completely"""
    print("\n🗂️  Removing Augment extensions...")

    removed_count = 0

    for ide_name, ext_name, ext_dir in extensions_found:
        ext_path = os.path.join(ext_dir, ext_name)

        try:
            if os.path.exists(ext_path):
                # Create backup of extension folder
                backup_path = ext_path + ".backup"
                if os.path.exists(backup_path):
                    shutil.rmtree(backup_path)
                shutil.copytree(ext_path, backup_path)
                print(f"   ✅ Created extension backup: {ext_name}.backup")

                # Remove the extension
                shutil.rmtree(ext_path)
                print(f"   ✅ {ide_name}: Removed extension {ext_name}")
                removed_count += 1

        except Exception as e:
            print(f"   ❌ {ide_name}: Error removing extension - {str(e)}")

    return removed_count

def clean_augment_temp_files(temp_files):
    """Remove Augment temporary files and cache"""
    print("\n🗃️  Cleaning temporary files and cache...")

    removed_count = 0

    for file_path in temp_files:
        try:
            if os.path.exists(file_path):
                if os.path.isdir(file_path):
                    shutil.rmtree(file_path)
                    print(f"   ✅ Removed directory: {os.path.basename(file_path)}")
                else:
                    os.remove(file_path)
                    print(f"   ✅ Removed file: {os.path.basename(file_path)}")
                removed_count += 1

        except Exception as e:
            print(f"   ❌ Error removing {os.path.basename(file_path)}: {str(e)}")

    return removed_count

def clean_augment_settings(settings_found):
    """Clean Augment settings from VSCode configuration"""
    print("\n⚙️  Cleaning VSCode settings...")

    cleaned_count = 0

    for ide_name, settings_file in settings_found:
        try:
            # Read current settings
            with open(settings_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # Create backup
            backup_file = settings_file + ".bak"
            with open(backup_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"   ✅ Created settings backup: {os.path.basename(backup_file)}")

            # Remove Augment-related settings (this is a simple approach)
            lines = content.split('\n')
            cleaned_lines = []

            for line in lines:
                if 'augment' in line.lower() and any(char in line for char in ['"', "'"]):
                    print(f"   🧹 Removing setting: {line.strip()}")
                    cleaned_count += 1
                    continue
                cleaned_lines.append(line)

            # Write cleaned settings
            with open(settings_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(cleaned_lines))

            print(f"   ✅ {ide_name}: Cleaned {cleaned_count} Augment settings")

        except Exception as e:
            print(f"   ❌ {ide_name}: Error cleaning settings - {str(e)}")

    return cleaned_count

def get_user_confirmation(total_entries):
    """Ask user for confirmation to proceed with cleanup"""
    print("\n" + "=" * 60)
    print("⚠️  CLEANUP CONFIRMATION")
    print("=" * 60)
    print(f"Found {total_entries} Augment-related entries in VSCode databases.")
    print()
    print("This script will:")
    print("  • Create backup files (.bak) before making changes")
    print("  • Remove all Augment-related entries from state.vscdb files")
    print("  • This action cannot be easily undone (except from backups)")
    print()

    while True:
        response = input("Do you want to proceed with cleanup? (y/yes or n/no): ").strip().lower()
        if response in ['y', 'yes']:
            return True
        elif response in ['n', 'no']:
            return False
        else:
            print("Please enter 'y' for yes or 'n' for no.")

def show_help():
    """Display usage instructions"""
    print("Augment Code Cleaner for Windows VSCode")
    print("=" * 40)
    print()
    print("Usage:")
    print("  python new.py              # Interactive cleanup")
    print("  python new.py --help       # Show this help")
    print()
    print("The script will:")
    print("  ✓ Scan for VSCode-based IDEs (VSCode, Insiders, Cursor)")
    print("  ✓ Check for Augment extensions and database entries")
    print("  ✓ Ask for confirmation before making changes")
    print("  ✓ Create backups before cleaning")
    print("  ✓ Remove all Augment-related entries")
    print()

def main():
    """Main function - Interactive Augment cleaner for VSCode"""
    try:
        # Check for help argument
        args = [arg.lower() for arg in sys.argv[1:]]
        help_mode = any(arg in ['--help', '-h', 'help', '?'] for arg in args)

        if help_mode:
            show_help()
            return

        print("Augment Code Cleaner for Windows VSCode")
        print("=" * 40)
        print()

        # Step 1: Scan for Augment installations and entries
        scan_results = scan_augment_entries()

        if scan_results is None:
            # No VSCode found
            print("\nPress Enter to exit...")
            input()
            return

        # Unpack scan results
        entries_by_ide = scan_results['entries_by_ide']
        total_entries = scan_results['total_entries']
        extensions = scan_results['extensions']
        settings = scan_results['settings']
        temp_files = scan_results['temp_files']
        registry_entries = scan_results['registry_entries']
        env_vars = scan_results['env_vars']
        network_traces = scan_results['network_traces']

        # Calculate total items found (not just database entries)
        total_items = (total_entries + len(extensions) + len(settings) +
                      len(temp_files) + len(registry_entries) + len(env_vars) + len(network_traces))

        if total_items == 0:
            print("\n✅ Great! No Augment installations found on your system.")
            print("Your system appears to be clean.")
        else:
            print(f"\n⚠️  AUGMENT CODE DETECTED!")
            print("=" * 50)
            print(f"📊 Summary of findings:")
            if extensions:
                print(f"   • {len(extensions)} Extension(s) installed")
            if total_entries > 0:
                print(f"   • {total_entries} Database entries")
            if settings:
                print(f"   • {len(settings)} Settings files with Augment config")
            if temp_files:
                print(f"   • {len(temp_files)} Temporary files/directories")
            if registry_entries:
                print(f"   • {len(registry_entries)} Registry entries")
            if env_vars:
                print(f"   • {len(env_vars)} Environment variables")
            if network_traces:
                print(f"   • {len(network_traces)} Network/API traces")

            print(f"\n📋 Total items to clean: {total_items}")

            # Step 2: Ask for user confirmation
            if get_user_confirmation(total_items):
                print("\n🚀 Starting complete Augment removal...")

                total_cleaned = 0

                # Step 3: Clean everything
                if extensions:
                    total_cleaned += clean_augment_extensions(extensions)

                if entries_by_ide:
                    total_cleaned += clean_augment_entries(entries_by_ide)

                if temp_files:
                    total_cleaned += clean_augment_temp_files(temp_files)

                if settings:
                    total_cleaned += clean_augment_settings(settings)

                print(f"\n🎉 COMPLETE AUGMENT REMOVAL FINISHED!")
                print("=" * 50)
                print(f"✅ Total items removed: {total_cleaned}")
                print(f"✅ All session data cleared")
                print(f"✅ Authentication tokens removed")
                print(f"✅ Extension files deleted")
                print(f"✅ Temporary files cleaned")
                print(f"✅ Backup files created for safety")
                print(f"\n⚠️  IMPORTANT: Restart VSCode to complete the removal!")
                print(f"🔓 You are now logged out of Augment Code")

            else:
                print("\n❌ Cleanup cancelled by user.")
                print("No changes were made to your system.")

        print("\nPress Enter to exit...")
        input()

    except KeyboardInterrupt:
        print("\n\nOperation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error: {str(e)}")
        print("Press Enter to exit...")
        input()
        sys.exit(1)

if __name__ == "__main__":
    main()

    